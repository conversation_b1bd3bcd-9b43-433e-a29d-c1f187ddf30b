# MySQL Migration - Installation Instructions

## Prerequisites

1. **MySQL Server** (version 8.0 or higher recommended)
2. **Node.js** (version 16 or higher)
3. **npm** or **yarn** package manager

## Step-by-Step Installation

### 1. Install MySQL Server

#### On Windows:
- Download MySQL installer from [mysql.com](https://dev.mysql.com/downloads/installer/)
- Run installer and choose "Developer Default"
- Set root password during installation

#### On macOS:
```bash
# Using Homebrew
brew install mysql
brew services start mysql

# Set root password
mysql_secure_installation
```

#### On Ubuntu/Debian:
```bash
sudo apt update
sudo apt install mysql-server
sudo mysql_secure_installation
```

### 2. Create Database

```bash
# Connect to MySQL
mysql -u root -p

# Create database
CREATE DATABASE aldallah_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# Create user (optional, for security)
CREATE USER 'aldallah_user'@'localhost' IDENTIFIED BY 'your_secure_password';
GRANT ALL PRIVILEGES ON aldallah_db.* TO 'aldallah_user'@'localhost';
FLUSH PRIVILEGES;

# Exit MySQL
EXIT;
```

### 3. Run Database Schema

```bash
# From project root directory
mysql -u root -p aldallah_db < database/mysql_schema.sql
```

### 4. Install Node.js Dependencies

```bash
# Remove old dependencies (if migrating)
rm -rf node_modules package-lock.json

# Install new dependencies
npm install
```

### 5. Configure Environment Variables

```bash
# Copy example environment file
cp .env.example .env

# Edit .env file with your MySQL credentials
```

Example `.env` configuration:
```env
# MySQL Database Configuration
DATABASE_HOST=localhost
DATABASE_PORT=3306
DATABASE_NAME=aldallah_db
DATABASE_USER=root
DATABASE_PASSWORD=your_mysql_password

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here_make_it_long_and_random

# Email Configuration
MAIL_USER=<EMAIL>
MAIL_PASS=your_app_password

# Server Configuration
PORT=5000
NODE_ENV=development

# Cloudinary Configuration (for image uploads)
CLOUDINARY_CLOUD_NAME=your_cloudinary_cloud_name
CLOUDINARY_API_KEY=your_cloudinary_api_key
CLOUDINARY_API_SECRET=your_cloudinary_api_secret
```

### 6. Start the Application

```bash
# Development mode
npm run dev

# Production mode
npm start
```

### 7. Verify Installation

1. **Check server startup:**
   - Look for "MySQL Connected: localhost:3306" message
   - Look for "Database synchronized" message
   - Server should start on http://localhost:5000

2. **Test health endpoint:**
   ```bash
   curl http://localhost:5000/health
   ```

3. **Test API documentation:**
   - Visit http://localhost:5000/api-docs

## Troubleshooting

### Common Issues

#### 1. MySQL Connection Error
```
Error: connect ECONNREFUSED 127.0.0.1:3306
```
**Solution:**
- Ensure MySQL server is running
- Check DATABASE_HOST and DATABASE_PORT in .env
- Verify MySQL credentials

#### 2. Database Access Denied
```
Error: Access denied for user 'root'@'localhost'
```
**Solution:**
- Check DATABASE_USER and DATABASE_PASSWORD in .env
- Ensure user has proper privileges
- Try connecting manually: `mysql -u root -p`

#### 3. Table Doesn't Exist
```
Error: Table 'aldallah_db.step1_users' doesn't exist
```
**Solution:**
- Run the schema file: `mysql -u root -p aldallah_db < database/mysql_schema.sql`
- Check if database was created properly

#### 4. Sequelize Sync Issues
```
Error: Sequelize sync failed
```
**Solution:**
- Set NODE_ENV=development in .env
- Check model definitions for syntax errors
- Verify foreign key relationships

### Performance Optimization

#### 1. MySQL Configuration
Add to MySQL configuration file (`my.cnf` or `my.ini`):
```ini
[mysqld]
innodb_buffer_pool_size = 256M
max_connections = 100
query_cache_size = 64M
query_cache_type = 1
```

#### 2. Connection Pool Tuning
Adjust in `config/database.js`:
```javascript
pool: {
  max: 20,          // Increase for high traffic
  min: 5,           // Minimum connections
  acquire: 30000,   // Connection timeout
  idle: 10000,      // Idle timeout
}
```

## Migration from MongoDB (Optional)

If you have existing MongoDB data, create a migration script:

```javascript
// migration/migrate-data.js
const mongoose = require('mongoose');
const { sequelize, Step1, Step2 } = require('../models');

async function migrateData() {
  // Connect to MongoDB
  await mongoose.connect(process.env.MONGO_URI);
  
  // Connect to MySQL
  await sequelize.authenticate();
  
  // Migrate Step1 data
  const mongoStep1 = await mongoose.model('Step1').find();
  for (const doc of mongoStep1) {
    await Step1.create({
      language: doc.language,
      country: doc.country,
      phoneNumber: doc.phoneNumber,
      email: doc.email,
      otp: doc.otp,
      otpCreatedAt: doc.otpCreatedAt,
      createdAt: doc.createdAt,
      updatedAt: doc.updatedAt
    });
  }
  
  // Continue with other models...
}

migrateData().catch(console.error);
```

## Testing

### Unit Tests
```bash
# Install testing dependencies
npm install --save-dev jest supertest

# Run tests
npm test
```

### API Testing
Use tools like Postman or curl to test endpoints:

```bash
# Test user registration
curl -X POST http://localhost:5000/api/step1 \
  -H "Content-Type: application/json" \
  -d '{"language":"English","country":"USA","phoneNumber":"+1234567890","email":"<EMAIL>"}'
```

## Production Deployment

### 1. Environment Setup
```env
NODE_ENV=production
DATABASE_HOST=your_production_db_host
DATABASE_PASSWORD=secure_production_password
```

### 2. Security Considerations
- Use SSL for database connections
- Set up proper firewall rules
- Use environment-specific JWT secrets
- Enable MySQL slow query log
- Set up database backups

### 3. Process Management
```bash
# Using PM2
npm install -g pm2
pm2 start server.js --name "aldallah-api"
pm2 startup
pm2 save
```

## Support

If you encounter issues:
1. Check the logs for detailed error messages
2. Verify all environment variables are set correctly
3. Ensure MySQL server is running and accessible
4. Test database connection manually
5. Check firewall and network settings

For additional help, refer to:
- [Sequelize Documentation](https://sequelize.org/docs/v6/)
- [MySQL Documentation](https://dev.mysql.com/doc/)
- [Node.js MySQL2 Driver](https://github.com/sidorares/node-mysql2)
