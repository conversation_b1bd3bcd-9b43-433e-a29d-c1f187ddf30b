const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const Step2 = sequelize.define('step2_users', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  step1Id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    field: 'step1_id',
    references: {
      model: 'step1_users',
      key: 'id',
    },
    onDelete: 'CASCADE',
  },
  language: {
    type: DataTypes.STRING(50),
    allowNull: false,
  },
  country: {
    type: DataTypes.STRING(100),
    allowNull: false,
  },
  phoneNumber: {
    type: DataTypes.STRING(20),
    allowNull: false,
    field: 'phone_number',
  },
  tempPhoneNumber: {
    type: DataTypes.STRING(20),
    allowNull: true,
    field: 'temp_phone_number',
  },
  email: {
    type: DataTypes.STRING(255),
    allowNull: false,
    unique: true,
    validate: {
      isEmail: true,
    },
  },
  username: {
    type: DataTypes.STRING(100),
    allowNull: false,
  },
  image: {
    type: DataTypes.TEXT,
    allowNull: false,
  },
  otp: {
    type: DataTypes.STRING(10),
    allowNull: true,
  },
  otpCreatedAt: {
    type: DataTypes.DATE,
    allowNull: true,
    defaultValue: DataTypes.NOW,
    field: 'otp_created_at',
  },
  // Chat-related fields
  isOnline: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    field: 'is_online',
  },
  lastSeen: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
    field: 'last_seen',
  },
  socketId: {
    type: DataTypes.STRING(255),
    allowNull: true,
    field: 'socket_id',
  },
  createdAt: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
    field: 'created_at',
  },
  updatedAt: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
    field: 'updated_at',
  },
}, {
  tableName: 'step2_users',
  timestamps: true,
  underscored: true,
  indexes: [
    {
      fields: ['step1_id'],
    },
    {
      fields: ['email'],
    },
    {
      fields: ['username'],
    },
    {
      fields: ['phone_number'],
    },
    {
      fields: ['is_online'],
    },
    {
      fields: ['socket_id'],
    },
    {
      fields: ['is_online', 'last_seen'],
    },
  ],
});

// Define associations
Step2.associate = (models) => {
  Step2.belongsTo(models.Step1, {
    foreignKey: 'step1Id',
    as: 'step1User',
  });
};

module.exports = Step2;
