
const sequelize = require('./database');

const connectDB = async () => {
  try {
    // Test the connection
    await sequelize.authenticate();
    console.log(`MySQL Connected: ${process.env.DATABASE_HOST || 'localhost'}:${process.env.DATABASE_PORT || 3306}`);

    // Import all models to register them with Sequelize
    require('../models/Step1.Model');
    require('../models/Step2.Model');
    require('../models/Conversation');
    require('../models/Message');
    require('../models/Group');

    // Sync models (create tables if they don't exist)
    // In production, you should use migrations instead
    if (process.env.NODE_ENV === 'development') {
      await sequelize.sync({ alter: false }); // Set to true only for development
      console.log('Database synchronized');
    }

    return sequelize;
  } catch (error) {
    console.error(`Error connecting to MySQL: ${error.message}`);
    console.error('Please ensure MySQL is running and credentials are correct');
    process.exit(1);
  }
};

module.exports = connectDB;
