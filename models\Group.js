const mongoose = require('mongoose');

const GroupSchema = new mongoose.Schema({
  conversation: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Conversation',
    required: true
  },
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100
  },
  description: {
    type: String,
    trim: true,
    maxlength: 500
  },
  image: String,
  admin: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Step2',
    required: true
  },
  admins: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Step2'
  }],
  members: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Step2',
      required: true
    },
    joinedAt: {
      type: Date,
      default: Date.now
    },
    role: {
      type: String,
      enum: ['admin', 'member'],
      default: 'member'
    }
  }],
  settings: {
    allowMembersToAddOthers: {
      type: Boolean,
      default: false
    },
    onlyAdminsCanSend: {
      type: Boolean,
      default: false
    },
    maxMembers: {
      type: Number,
      default: 256,
      min: 2,
      max: 1000
    }
  },
  isActive: {
    type: Boolean,
    default: true
  },
  lastActivity: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Virtual for member count
GroupSchema.virtual('memberCount').get(function() {
  return this.members ? this.members.length : 0;
});

// Index for better query performance
GroupSchema.index({ admin: 1 });
GroupSchema.index({ 'members.user': 1 });
GroupSchema.index({ isActive: 1, lastActivity: -1 });

// Pre-save middleware to ensure admin is in members array
GroupSchema.pre('save', function(next) {
  if (this.isNew || this.isModified('admin')) {
    // Check if admin is already in members array
    const adminInMembers = this.members.some(member =>
      member.user.toString() === this.admin.toString()
    );

    if (!adminInMembers) {
      this.members.push({
        user: this.admin,
        role: 'admin',
        joinedAt: new Date()
      });
    }

    // Ensure admin is in admins array
    if (!this.admins.includes(this.admin)) {
      this.admins.push(this.admin);
    }
  }
  next();
});

// Method to check if user is admin
GroupSchema.methods.isUserAdmin = function(userId) {
  return this.admins.some(adminId => adminId.toString() === userId.toString());
};

// Method to check if user is member
GroupSchema.methods.isUserMember = function(userId) {
  return this.members.some(member => member.user.toString() === userId.toString());
};

// Method to get member role
GroupSchema.methods.getMemberRole = function(userId) {
  const member = this.members.find(member => member.user.toString() === userId.toString());
  return member ? member.role : null;
};

module.exports = mongoose.model('Group', GroupSchema);