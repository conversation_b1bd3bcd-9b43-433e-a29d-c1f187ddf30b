const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const Group = sequelize.define('groups', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  conversationId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    unique: true,
    field: 'conversation_id',
    references: {
      model: 'conversations',
      key: 'id',
    },
    onDelete: 'CASCADE',
  },
  name: {
    type: DataTypes.STRING(100),
    allowNull: false,
    validate: {
      len: [1, 100],
    },
  },
  description: {
    type: DataTypes.STRING(500),
    allowNull: true,
    validate: {
      len: [0, 500],
    },
  },
  image: {
    type: DataTypes.TEXT,
    allowNull: true,
  },
  adminId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    field: 'admin_id',
    references: {
      model: 'step2_users',
      key: 'id',
    },
    onDelete: 'CASCADE',
  },
  // Settings
  allowMembersToAddOthers: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    field: 'allow_members_to_add_others',
  },
  onlyAdminsCanSend: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    field: 'only_admins_can_send',
  },
  maxMembers: {
    type: DataTypes.INTEGER,
    defaultValue: 256,
    field: 'max_members',
    validate: {
      min: 2,
      max: 1000,
    },
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    field: 'is_active',
  },
  lastActivity: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
    field: 'last_activity',
  },
  createdAt: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
    field: 'created_at',
  },
  updatedAt: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
    field: 'updated_at',
  },
}, {
  tableName: 'groups',
  timestamps: true,
  underscored: true,
  indexes: [
    {
      fields: ['conversation_id'],
    },
    {
      fields: ['admin_id'],
    },
    {
      fields: ['is_active'],
    },
    {
      fields: ['last_activity'],
    },
    {
      fields: ['is_active', 'last_activity'],
    },
  ],
});

// Define the GroupMember junction table
const GroupMember = sequelize.define('group_members', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  groupId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    field: 'group_id',
    references: {
      model: 'groups',
      key: 'id',
    },
    onDelete: 'CASCADE',
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    field: 'user_id',
    references: {
      model: 'step2_users',
      key: 'id',
    },
    onDelete: 'CASCADE',
  },
  role: {
    type: DataTypes.ENUM('admin', 'member'),
    defaultValue: 'member',
  },
  joinedAt: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
    field: 'joined_at',
  },
}, {
  tableName: 'group_members',
  timestamps: false,
  underscored: true,
  indexes: [
    {
      fields: ['group_id'],
    },
    {
      fields: ['user_id'],
    },
    {
      fields: ['role'],
    },
    {
      unique: true,
      fields: ['group_id', 'user_id'],
    },
  ],
});

// Define associations
Group.associate = (models) => {
  // Belongs to conversation
  Group.belongsTo(models.Conversation, {
    foreignKey: 'conversationId',
    as: 'conversation',
  });

  // Belongs to admin user
  Group.belongsTo(models.Step2, {
    foreignKey: 'adminId',
    as: 'admin',
  });

  // Many-to-many relationship with Step2 users through GroupMember
  Group.belongsToMany(models.Step2, {
    through: GroupMember,
    foreignKey: 'groupId',
    otherKey: 'userId',
    as: 'members',
  });

  // One-to-many relationship with GroupMember for more detailed member info
  Group.hasMany(GroupMember, {
    foreignKey: 'groupId',
    as: 'memberDetails',
  });
};

// Instance methods
Group.prototype.isUserAdmin = async function(userId) {
  const member = await GroupMember.findOne({
    where: {
      groupId: this.id,
      userId: userId,
      role: 'admin',
    },
  });
  return !!member;
};

Group.prototype.isUserMember = async function(userId) {
  const member = await GroupMember.findOne({
    where: {
      groupId: this.id,
      userId: userId,
    },
  });
  return !!member;
};

Group.prototype.getMemberRole = async function(userId) {
  const member = await GroupMember.findOne({
    where: {
      groupId: this.id,
      userId: userId,
    },
  });
  return member ? member.role : null;
};

Group.prototype.getMemberCount = async function() {
  const count = await GroupMember.count({
    where: {
      groupId: this.id,
    },
  });
  return count;
};

Group.prototype.getAdmins = async function() {
  const adminMembers = await GroupMember.findAll({
    where: {
      groupId: this.id,
      role: 'admin',
    },
    include: [{
      model: require('./Step2.Model'),
      as: 'user',
      attributes: ['id', 'username', 'image'],
    }],
  });
  return adminMembers.map(member => member.user);
};

// Hooks (equivalent to Mongoose pre-save middleware)
Group.addHook('afterCreate', async (group, options) => {
  // Ensure admin is added as a member with admin role
  await GroupMember.findOrCreate({
    where: {
      groupId: group.id,
      userId: group.adminId,
    },
    defaults: {
      role: 'admin',
      joinedAt: new Date(),
    },
  });
});

Group.addHook('afterUpdate', async (group, options) => {
  // If admin changed, ensure new admin is in members with admin role
  if (group.changed('adminId')) {
    await GroupMember.findOrCreate({
      where: {
        groupId: group.id,
        userId: group.adminId,
      },
      defaults: {
        role: 'admin',
        joinedAt: new Date(),
      },
    });

    // Update the member's role to admin if they were already a member
    await GroupMember.update(
      { role: 'admin' },
      {
        where: {
          groupId: group.id,
          userId: group.adminId,
        },
      }
    );
  }
});

// Export all related models
module.exports = {
  Group,
  GroupMember,
};