const Conversation = require('../models/Conversation');
const Message = require('../models/Message');
const Step2 = require('../models/Step2.Model');



const createOrGetConversation = async (req, res) => {
  try {
    const { participantId } = req.body;
    const { userId } = req.params;

    if (!participantId || !userId) {
      return res.status(400).json({
        status: 400,
        success: false,
        message: 'Both userId and participantId are required'
      });
    }


    const [user1, user2] = await Promise.all([
      Step2.findById(userId),
      Step2.findById(participantId)
    ]);

    if (!user1 || !user2) {
      return res.status(404).json({
        status: 404,
        success: false,
        message: 'One or both users not found'
      });
    }


    let conversation = await Conversation.findOne({
      participants: { $all: [userId, participantId] },
      isGroup: false
    }).populate('participants', 'username image')
      .populate('lastMessage');


    if (!conversation) {
      conversation = new Conversation({
        participants: [userId, participantId],
        isGroup: false,
        unreadCounts: [
          { user: userId, count: 0 },
          { user: participantId, count: 0 }
        ]
      });
      await conversation.save();
      await conversation.populate('participants', 'username image');
    }

    res.status(200).json({
      status: 200,
      success: true,
      data: conversation
    });
  } catch (error) {
    console.error('Error creating/getting conversation:', error);
    res.status(500).json({
      status: 500,
      success: false,
      message: 'Internal server error'
    });
  }
};



const getUserConversations = async (req, res) => {
  try {
    const { userId } = req.params;

    if (!userId) {
      return res.status(400).json({
        status: 400,
        success: false,
        message: 'User ID is required'
      });
    }

    const conversations = await Conversation.find({
      participants: userId
    })
    .populate('participants', 'username image')
    .populate('lastMessage')
    .sort({ updatedAt: -1 });

    res.status(200).json({
      status: 200,
      success: true,
      data: conversations
    });
  } catch (error) {
    console.error('Error fetching conversations:', error);
    res.status(500).json({
      status: 500,
      success: false,
      message: 'Internal server error'
    });
  }
};


const sendMessage = async (req, res) => {
  try {
    const { conversationId } = req.params;
    const { senderId, content, messageType = 'text', lat, lng, address } = req.body;


    if (!conversationId || !senderId) {
      return res.status(400).json({
        status: 400,
        success: false,
        message: 'Conversation ID and sender ID are required'
      });
    }


    if (messageType === 'location') {

      if (lat === undefined || lng === undefined) {
        return res.status(400).json({
          status: 400,
          success: false,
          message: 'Latitude and longitude are required for location messages'
        });
      }


      if (typeof lat !== 'number' || typeof lng !== 'number' ||
          lat < -90 || lat > 90 || lng < -180 || lng > 180) {
        return res.status(400).json({
          status: 400,
          success: false,
          message: 'Invalid coordinates. Latitude must be between -90 and 90, longitude between -180 and 180'
        });
      }
    } else {

      if (!content) {
        return res.status(400).json({
          status: 400,
          success: false,
          message: 'Content is required for text messages'
        });
      }
    }


    const conversation = await Conversation.findById(conversationId);
    if (!conversation) {
      return res.status(404).json({
        status: 404,
        success: false,
        message: 'Conversation not found'
      });
    }

    if (!conversation.participants.includes(senderId)) {
      return res.status(403).json({
        status: 403,
        success: false,
        message: 'Sender is not a participant in this conversation'
      });
    }

    const messageData = {
      conversation: conversationId,
      sender: senderId,
      content: content || '',
      messageType,
      deliveredTo: [senderId]
    };


    if (messageType === 'location') {
      messageData.location = {
        latitude: lat,
        longitude: lng,
        address: address || ''
      };
    }


    const message = new Message(messageData);

    await message.save();
    await message.populate('sender', 'username image');


    conversation.lastMessage = message._id;


    conversation.unreadCounts.forEach(unreadCount => {
      if (unreadCount.user.toString() !== senderId) {
        unreadCount.count += 1;
      }
    });

    await conversation.save();

    res.status(201).json({
      status: 201,
      success: true,
      data: message
    });
  } catch (error) {
    console.error('Error sending message:', error);
    res.status(500).json({
      status: 500,
      success: false,
      message: 'Internal server error'
    });
  }
};

const getMessages = async (req, res) => {
  try {
    const { conversationId } = req.params;
    const { page = 1, limit = 50 } = req.query;

    if (!conversationId) {
      return res.status(400).json({
        status: 400,
        success: false,
        message: 'Conversation ID is required'
      });
    }

    const skip = (page - 1) * limit;

    const messages = await Message.find({
      conversation: conversationId,
      isDeleted: false
    })
    .populate('sender', 'username image')
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(parseInt(limit));

    const totalMessages = await Message.countDocuments({
      conversation: conversationId,
      isDeleted: false
    });

    res.status(200).json({
      status: 200,
      success: true,
      data: {
        messages: messages.reverse(),
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(totalMessages / limit),
          totalMessages,
          hasMore: skip + messages.length < totalMessages
        }
      }
    });
  } catch (error) {
    console.error('Error fetching messages:', error);
    res.status(500).json({
      status: 500,
      success: false,
      message: 'Internal server error'
    });
  }
};


const markMessagesAsRead = async (req, res) => {
  try {
    const { conversationId } = req.params;
    const { userId } = req.body;

    if (!conversationId || !userId) {
      return res.status(400).json({
        status: 400,
        success: false,
        message: 'Conversation ID and user ID are required'
      });
    }


    await Message.updateMany(
      {
        conversation: conversationId,
        sender: { $ne: userId },
        readBy: { $ne: userId }
      },
      {
        $addToSet: { readBy: userId }
      }
    );


    await Conversation.updateOne(
      { _id: conversationId, 'unreadCounts.user': userId },
      { $set: { 'unreadCounts.$.count': 0 } }
    );

    res.status(200).json({
      status: 200,
      success: true,
      message: 'Messages marked as read'
    });
  } catch (error) {
    console.error('Error marking messages as read:', error);
    res.status(500).json({
      status: 500,
      success: false,
      message: 'Internal server error'
    });
  }
};


const markMessagesAsDelivered = async (req, res) => {
  try {
    const { conversationId } = req.params;
    const { userId } = req.body;

    if (!conversationId || !userId) {
      return res.status(400).json({
        status: 400,
        success: false,
        message: 'Conversation ID and user ID are required'
      });
    }

    
    await Message.updateMany(
      {
        conversation: conversationId,
        sender: { $ne: userId },
        deliveredTo: { $ne: userId }
      },
      {
        $addToSet: { deliveredTo: userId }
      }
    );

    res.status(200).json({
      status: 200,
      success: true,
      message: 'Messages marked as delivered'
    });
  } catch (error) {
    console.error('Error marking messages as delivered:', error);
    res.status(500).json({
      status: 500,
      success: false,
      message: 'Internal server error'
    });
  }
};

module.exports = {
  createOrGetConversation,
  getUserConversations,
  sendMessage,
  getMessages,
  markMessagesAsRead,
  markMessagesAsDelivered
};
