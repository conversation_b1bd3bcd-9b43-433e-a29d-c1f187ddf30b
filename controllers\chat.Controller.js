const {
  Conversation,
  ConversationParticipant,
  ConversationUnreadCount,
  Message,
  MessageReadStatus,
  MessageDeliveryStatus,
  Step2
} = require('../models');



const createOrGetConversation = async (req, res) => {
  try {
    const { participantId } = req.body;
    const { userId } = req.params;

    if (!participantId || !userId) {
      return res.status(400).json({
        status: 400,
        success: false,
        message: 'Both userId and participantId are required'
      });
    }

    // Check if both users exist
    const [user1, user2] = await Promise.all([
      Step2.findByPk(userId),
      Step2.findByPk(participantId)
    ]);

    if (!user1 || !user2) {
      return res.status(404).json({
        status: 404,
        success: false,
        message: 'One or both users not found'
      });
    }

    // Find existing conversation between these two users
    let conversation = await Conversation.findOne({
      where: {
        isGroup: false
      },
      include: [
        {
          model: Step2,
          as: 'participants',
          attributes: ['id', 'username', 'image'],
          through: { attributes: [] },
          where: {
            id: [userId, participantId]
          }
        },
        {
          model: Message,
          as: 'lastMessage',
          required: false,
          include: [{
            model: Step2,
            as: 'sender',
            attributes: ['id', 'username', 'image']
          }]
        }
      ]
    });

    // Check if conversation has both participants
    if (conversation && conversation.participants.length === 2) {
      const participantIds = conversation.participants.map(p => p.id);
      if (!participantIds.includes(parseInt(userId)) || !participantIds.includes(parseInt(participantId))) {
        conversation = null;
      }
    } else {
      conversation = null;
    }

    // Create new conversation if none exists
    if (!conversation) {
      conversation = await Conversation.create({
        isGroup: false
      });

      // Add participants
      await ConversationParticipant.bulkCreate([
        { conversationId: conversation.id, userId: userId },
        { conversationId: conversation.id, userId: participantId }
      ]);

      // Add unread counts
      await ConversationUnreadCount.bulkCreate([
        { conversationId: conversation.id, userId: userId, count: 0 },
        { conversationId: conversation.id, userId: participantId, count: 0 }
      ]);

      // Reload conversation with associations
      conversation = await Conversation.findByPk(conversation.id, {
        include: [
          {
            model: Step2,
            as: 'participants',
            attributes: ['id', 'username', 'image'],
            through: { attributes: [] }
          },
          {
            model: Message,
            as: 'lastMessage',
            required: false,
            include: [{
              model: Step2,
              as: 'sender',
              attributes: ['id', 'username', 'image']
            }]
          }
        ]
      });
    }

    res.status(200).json({
      status: 200,
      success: true,
      data: conversation
    });
  } catch (error) {
    console.error('Error creating/getting conversation:', error);
    res.status(500).json({
      status: 500,
      success: false,
      message: 'Internal server error'
    });
  }
};



const getUserConversations = async (req, res) => {
  try {
    const { userId } = req.params;

    if (!userId) {
      return res.status(400).json({
        status: 400,
        success: false,
        message: 'User ID is required'
      });
    }

    // Find all conversations where user is a participant
    const conversations = await Conversation.findAll({
      include: [
        {
          model: Step2,
          as: 'participants',
          attributes: ['id', 'username', 'image'],
          through: { attributes: [] },
          where: {
            id: userId
          }
        },
        {
          model: Message,
          as: 'lastMessage',
          required: false,
          include: [{
            model: Step2,
            as: 'sender',
            attributes: ['id', 'username', 'image']
          }]
        },
        {
          model: ConversationUnreadCount,
          as: 'unreadCounts',
          where: {
            userId: userId
          },
          required: false
        }
      ],
      order: [['updatedAt', 'DESC']]
    });

    // For each conversation, get all participants (not just the current user)
    const conversationsWithAllParticipants = await Promise.all(
      conversations.map(async (conversation) => {
        const allParticipants = await Step2.findAll({
          include: [{
            model: Conversation,
            as: 'conversations',
            where: { id: conversation.id },
            through: { attributes: [] }
          }],
          attributes: ['id', 'username', 'image']
        });

        return {
          ...conversation.toJSON(),
          participants: allParticipants
        };
      })
    );

    res.status(200).json({
      status: 200,
      success: true,
      data: conversationsWithAllParticipants
    });
  } catch (error) {
    console.error('Error fetching conversations:', error);
    res.status(500).json({
      status: 500,
      success: false,
      message: 'Internal server error'
    });
  }
};


const sendMessage = async (req, res) => {
  try {
    const { conversationId } = req.params;
    const { senderId, content, messageType = 'text', lat, lng, address } = req.body;

    if (!conversationId || !senderId) {
      return res.status(400).json({
        status: 400,
        success: false,
        message: 'Conversation ID and sender ID are required'
      });
    }

    // Validate location messages
    if (messageType === 'location') {
      if (lat === undefined || lng === undefined) {
        return res.status(400).json({
          status: 400,
          success: false,
          message: 'Latitude and longitude are required for location messages'
        });
      }

      if (typeof lat !== 'number' || typeof lng !== 'number' ||
          lat < -90 || lat > 90 || lng < -180 || lng > 180) {
        return res.status(400).json({
          status: 400,
          success: false,
          message: 'Invalid coordinates. Latitude must be between -90 and 90, longitude between -180 and 180'
        });
      }
    } else {
      // Validate text messages
      if (!content) {
        return res.status(400).json({
          status: 400,
          success: false,
          message: 'Content is required for text messages'
        });
      }
    }

    // Check if conversation exists and sender is a participant
    const conversation = await Conversation.findByPk(conversationId, {
      include: [{
        model: Step2,
        as: 'participants',
        through: { attributes: [] }
      }]
    });

    if (!conversation) {
      return res.status(404).json({
        status: 404,
        success: false,
        message: 'Conversation not found'
      });
    }

    const participantIds = conversation.participants.map(p => p.id);
    if (!participantIds.includes(parseInt(senderId))) {
      return res.status(403).json({
        status: 403,
        success: false,
        message: 'Sender is not a participant in this conversation'
      });
    }

    // Prepare message data
    const messageData = {
      conversationId: conversationId,
      senderId: senderId,
      content: content || '',
      messageType
    };

    // Add location data if it's a location message
    if (messageType === 'location') {
      messageData.locationLatitude = lat;
      messageData.locationLongitude = lng;
      messageData.locationAddress = address || '';
    }

    // Create message
    const message = await Message.create(messageData);

    // Load message with sender info
    const messageWithSender = await Message.findByPk(message.id, {
      include: [{
        model: Step2,
        as: 'sender',
        attributes: ['id', 'username', 'image']
      }]
    });

    // Update conversation's last message
    await conversation.update({
      lastMessageId: message.id
    });

    // Update unread counts for other participants
    const otherParticipants = participantIds.filter(id => id !== parseInt(senderId));

    for (const participantId of otherParticipants) {
      await ConversationUnreadCount.increment('count', {
        where: {
          conversationId: conversationId,
          userId: participantId
        }
      });
    }

    // Add delivery status for sender (automatically delivered to sender)
    await MessageDeliveryStatus.create({
      messageId: message.id,
      userId: senderId
    });

    res.status(201).json({
      status: 201,
      success: true,
      data: messageWithSender
    });
  } catch (error) {
    console.error('Error sending message:', error);
    res.status(500).json({
      status: 500,
      success: false,
      message: 'Internal server error'
    });
  }
};

const getMessages = async (req, res) => {
  try {
    const { conversationId } = req.params;
    const { page = 1, limit = 50 } = req.query;

    if (!conversationId) {
      return res.status(400).json({
        status: 400,
        success: false,
        message: 'Conversation ID is required'
      });
    }

    const offset = (page - 1) * limit;

    const messages = await Message.findAll({
      where: {
        conversationId: conversationId,
        isDeleted: false
      },
      include: [{
        model: Step2,
        as: 'sender',
        attributes: ['id', 'username', 'image']
      }],
      order: [['createdAt', 'DESC']],
      offset: offset,
      limit: parseInt(limit)
    });

    const totalMessages = await Message.count({
      where: {
        conversationId: conversationId,
        isDeleted: false
      }
    });

    // Transform messages to include location object for compatibility
    const transformedMessages = messages.map(message => {
      const messageData = message.toJSON();

      // Add location object if location data exists
      if (messageData.locationLatitude && messageData.locationLongitude) {
        messageData.location = {
          latitude: parseFloat(messageData.locationLatitude),
          longitude: parseFloat(messageData.locationLongitude),
          address: messageData.locationAddress
        };
      }

      return messageData;
    });

    res.status(200).json({
      status: 200,
      success: true,
      data: {
        messages: transformedMessages.reverse(), // Reverse to show oldest first
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(totalMessages / limit),
          totalMessages,
          hasMore: offset + messages.length < totalMessages
        }
      }
    });
  } catch (error) {
    console.error('Error fetching messages:', error);
    res.status(500).json({
      status: 500,
      success: false,
      message: 'Internal server error'
    });
  }
};


const markMessagesAsRead = async (req, res) => {
  try {
    const { conversationId } = req.params;
    const { userId } = req.body;

    if (!conversationId || !userId) {
      return res.status(400).json({
        status: 400,
        success: false,
        message: 'Conversation ID and user ID are required'
      });
    }

    // Get all unread messages in the conversation that were not sent by this user
    const unreadMessages = await Message.findAll({
      where: {
        conversationId: conversationId,
        senderId: { [require('sequelize').Op.ne]: userId }
      },
      include: [{
        model: Step2,
        as: 'readBy',
        where: { id: userId },
        required: false,
        through: { attributes: [] }
      }]
    });

    // Filter messages that haven't been read by this user yet
    const messagesToMarkAsRead = unreadMessages.filter(message =>
      !message.readBy.some(reader => reader.id === parseInt(userId))
    );

    // Mark messages as read
    for (const message of messagesToMarkAsRead) {
      await MessageReadStatus.findOrCreate({
        where: {
          messageId: message.id,
          userId: userId
        },
        defaults: {
          readAt: new Date()
        }
      });
    }

    // Reset unread count for this user in this conversation
    await ConversationUnreadCount.update(
      { count: 0 },
      {
        where: {
          conversationId: conversationId,
          userId: userId
        }
      }
    );

    res.status(200).json({
      status: 200,
      success: true,
      message: 'Messages marked as read'
    });
  } catch (error) {
    console.error('Error marking messages as read:', error);
    res.status(500).json({
      status: 500,
      success: false,
      message: 'Internal server error'
    });
  }
};


const markMessagesAsDelivered = async (req, res) => {
  try {
    const { conversationId } = req.params;
    const { userId } = req.body;

    if (!conversationId || !userId) {
      return res.status(400).json({
        status: 400,
        success: false,
        message: 'Conversation ID and user ID are required'
      });
    }

    // Get all messages in the conversation that were not sent by this user
    const messages = await Message.findAll({
      where: {
        conversationId: conversationId,
        senderId: { [require('sequelize').Op.ne]: userId }
      },
      include: [{
        model: Step2,
        as: 'deliveredTo',
        where: { id: userId },
        required: false,
        through: { attributes: [] }
      }]
    });

    // Filter messages that haven't been delivered to this user yet
    const messagesToMarkAsDelivered = messages.filter(message =>
      !message.deliveredTo.some(recipient => recipient.id === parseInt(userId))
    );

    // Mark messages as delivered
    for (const message of messagesToMarkAsDelivered) {
      await MessageDeliveryStatus.findOrCreate({
        where: {
          messageId: message.id,
          userId: userId
        },
        defaults: {
          deliveredAt: new Date()
        }
      });
    }

    res.status(200).json({
      status: 200,
      success: true,
      message: 'Messages marked as delivered'
    });
  } catch (error) {
    console.error('Error marking messages as delivered:', error);
    res.status(500).json({
      status: 500,
      success: false,
      message: 'Internal server error'
    });
  }
};

module.exports = {
  createOrGetConversation,
  getUserConversations,
  sendMessage,
  getMessages,
  markMessagesAsRead,
  markMessagesAsDelivered
};
