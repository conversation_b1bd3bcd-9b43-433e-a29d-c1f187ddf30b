const express = require('express');
const multer = require('multer');
const { uploadUser, updateUser, getAlluser, getSpecific_user, deleteUser , changePhoneNumber, verifyOTP} = require('../controllers/step2.Controller');


const authMiddleware = require('../middleware/auth.middleware');

const router = express.Router();
const storage = multer.memoryStorage();
const upload = multer({ storage });

/**
 * @swagger
 * components:
 *   schemas:
 *     Step2:
 *       type: object
 *       properties:
 *         _id:
 *           type: string
 *           example: 64fbb7f1bca09a2e567b5678
 *         step1_id:
 *           type: string
 *           example: 64fbb7f1bca09a2e567b1234
 *         username:
 *           type: string
 *           example: <PERSON>
 *         email:
 *           type: string
 *           format: email
 *           example: <EMAIL>
 *         image:
 *           type: string
 *           example: https://res.cloudinary.com/demo/image/upload/v1692200000/sample.jpg
 *         language:
 *           type: string
 *           example: English
 *         country:
 *           type: string
 *           example: USA
 *         phoneNumber:
 *           type: string
 *           example: +123456789
 *         otp:
 *           type: string
 *           example: 123456
 *         otpCreatedAt:
 *           type: string
 *           format: date-time
 *           example: 2025-08-15T12:00:00Z
 *   securitySchemes:
 *     bearerAuth:
 *       type: http
 *       scheme: bearer
 *       bearerFormat: JWT
 */


/**
 * @swagger
 * '/api/step2/upload-user':
 *   post:
 *     summary: Create a new user (Step 2) and upload profile image
 *     tags: [Step2]
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             required:
 *               - username
 *               - email
 *               - User_id
 *               - image
 *             properties:
 *               username:
 *                 type: string
 *                 example: John Doe
 *               email:
 *                 type: string
 *                 format: email
 *                 example: <EMAIL>
 *               User_id:
 *                 type: string
 *                 description: The ID of the Step1 user
 *                 example: 64fbb7f1bca09a2e567b1234
 *               image:
 *                 type: string
 *                 format: binary
 *     responses:
 *       201:
 *         description: User created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: integer
 *                   example: 201
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: User uploaded successfully
 *                 data:
 *                   $ref: '#/components/schemas/Step2'
 *                 token:
 *                   type: string
 *                   example: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
 *       400:
 *         description: Bad request (missing or invalid data)
 *       404:
 *         description: Step1 user not found
 *       500:
 *         description: Internal Server Error
 */

router.post('/upload-user', upload.single('image'), uploadUser);

/**
 * @swagger
 * '/api/step2/update-user/{id}':
 *   put:
 *     summary: Update an existing user profile
 *     tags: [Step2]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: The ID of the Step2 user to update
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             required:
 *               - username
 *               - email
 *             properties:
 *               username:
 *                 type: string
 *                 example: John Updated
 *               email:
 *                 type: string
 *                 format: email
 *                 example: <EMAIL>
 *               image:
 *                 type: string
 *                 format: binary
 *                 description: Optional updated profile image
 *     responses:
 *       200:
 *         description: User updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: integer
 *                   example: 200
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: User updated successfully
 *                 data:
 *                   $ref: '#/components/schemas/Step2'
 *       400:
 *         description: Bad request (missing or invalid data)
 *       404:
 *         description: User not found
 *       500:
 *         description: Internal Server Error
 */

router.put('/update-user/:id', upload.single('image'), authMiddleware ,updateUser);
/**
 * @swagger
 * '/api/step2/all-users':
 *   get:
 *     summary: Get all Step2 users
 *     tags: [Step2]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: List of all users retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: integer
 *                   example: 200
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Step2'
 *       401:
 *         description: Unauthorized - Missing or invalid token
 *       500:
 *         description: Internal Server Error
 */
router.get('/all-users', authMiddleware, getAlluser);

/**
 * @swagger
 * '/api/step2/user/{id}':
 *   get:
 *     summary: Get a specific Step2 user by ID
 *     tags: [Step2]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: MongoDB ObjectId of the Step2 user
 *     responses:
 *       200:
 *         description: User retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: integer
 *                   example: 200
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: User found
 *                 data:
 *                   $ref: '#/components/schemas/Step2'
 *       401:
 *         description: Unauthorized - Missing or invalid token
 *       404:
 *         description: User not found
 *       500:
 *         description: Internal Server Error
 */
router.get('/user/:id', authMiddleware, getSpecific_user);
/**
 * @swagger
 * /api/step2/delete:
 *   delete:
 *     summary: Delete the authenticated user
 *     description: Deletes the authenticated Step2 user and the associated Step1 record.
 *     tags: [Step2]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: User deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: integer
 *                   example: 200
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: User deleted successfully
 *                 data:
 *                   $ref: '#/components/schemas/Step2'
 *       401:
 *         description: Unauthorized or token invalid
 *       404:
 *         description: User not found
 *       500:
 *         description: Internal Server Error
 */
router.delete('/delete', authMiddleware, deleteUser);


/**
 * @swagger
 * /api/step2/change-phone:
 *   post:
 *     summary: Send OTP to user's email before changing phone number
 *     description: Authenticated users can request to change their phone number. An OTP will be sent to their registered email.
 *     tags: [Step2]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - phoneNumber
 *             properties:
 *               phoneNumber:
 *                 type: string
 *                 example: "+1234567890"
 *     responses:
 *       200:
 *         description: OTP sent successfully to user's email
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: integer
 *                   example: 200
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: OTP sent successfully
 *                 data:
 *                   $ref: '#/components/schemas/Step2'
 *       400:
 *         description: Invalid request or same phone number as old one
 *       401:
 *         description: Unauthorized or token invalid
 *       404:
 *         description: User not found
 *       500:
 *         description: Internal Server Error
 */
router.post('/change-phone', authMiddleware, changePhoneNumber);

/**
 * @swagger
 * /api/step2/change-phone/verify:
 *   post:
 *     summary: Verify OTP and change phone number
 *     description: Authenticated users verify the OTP sent to their email to confirm phone number change.
 *     tags: [Step2]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - otp
 *             properties:
 *               otp:
 *                 type: string
 *                 example: "123456"
 *     responses:
 *       200:
 *         description: Phone number changed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: integer
 *                   example: 200
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Phone number changed successfully
 *                 data:
 *                   $ref: '#/components/schemas/Step2'
 *       400:
 *         description: Invalid request or expired OTP
 *       401:
 *         description: Unauthorized or token invalid
 *       404:
 *         description: User not found
 *       500:
 *         description: Internal Server Error
 */
router.post('/change-phone/verify', authMiddleware, verifyOTP);
module.exports = router;
