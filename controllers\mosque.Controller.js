const mosqueService = require('../utils/mosqueService');

class MosqueController {
  /**
   * Find nearby mosques based on client IP location or provided coordinates
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async findNearbyMosques(req, res) {
    try {

      const clientIP = MosqueController.prototype.getClientIP(req);


      const {
        radius = 5000,
        latitude,
        longitude
      } = req.query;


      const searchRadius = parseInt(radius);
      if (isNaN(searchRadius) || searchRadius < 100 || searchRadius > 50000) {
        return res.status(400).json({
          status: 400,
          success: false,
          error: 'Invalid radius. Must be between 100 and 50000 meters.',
          code: 'INVALID_RADIUS'
        });
      }


      let lat, lng;
      if (latitude !== undefined || longitude !== undefined) {
        lat = parseFloat(latitude);
        lng = parseFloat(longitude);

        if (isNaN(lat) || isNaN(lng)) {
          return res.status(400).json({
            status: 400,
            success: false,
            error: 'Invalid coordinates. Both latitude and longitude must be valid numbers.',
            code: 'INVALID_COORDINATES'
          });
        }

        if (lat < -90 || lat > 90) {
          return res.status(400).json({
            status: 400,
            success: false,
            error: 'Invalid latitude. Must be between -90 and 90.',
            code: 'INVALID_LATITUDE'
          });
        }

        if (lng < -180 || lng > 180) {
          return res.status(400).json({
            status: 400,
            success: false,
            error: 'Invalid longitude. Must be between -180 and 180.',
            code: 'INVALID_LONGITUDE'
          });
        }
      }

      const locationInfo = lat !== undefined && lng !== undefined
        ? `coordinates: ${lat}, ${lng}`
        : `IP: ${clientIP}`;
      console.log(`Finding mosques for ${locationInfo}, radius: ${searchRadius}m`);


      const result = await mosqueService.findNearbyMosques(clientIP, {
        radius: searchRadius,
        latitude: lat,
        longitude: lng
      });


      delete result.mapData;


      result.requestInfo = {
        clientIP: clientIP,
        searchRadius: searchRadius,
        dataSource: result.mosques.dataSource,
        freeServicesOnly: true,
        apiType: 'backend-only',
        locationSource: result.location.source,
        coordinatesProvided: lat !== undefined && lng !== undefined
      };

      res.status(200).json({
        status: 200,
        success: true,
        data: result});

    } catch (error) {
      console.error('Error in findNearbyMosques controller:', error.message);


      if (error.message.includes('Unable to detect location')) {
        return res.status(400).json({
          status: 400,
          success: false,
          error: 'Unable to detect your location from IP address. Please provide coordinates or try again.',
          code: 'LOCATION_DETECTION_FAILED'
        });
      }

      if (error.message.includes('OpenStreetMap') || error.message.includes('Overpass')) {
        return res.status(500).json({
          status: 500,
          success: false,
          error: 'Mosque data service temporarily unavailable. Please try again later.',
          code: 'SERVICE_UNAVAILABLE'
        });
      }

      if (error.message.includes('Unable to find nearby mosques')) {
        return res.status(404).json({
          status: 404,
          success: false,
          error: 'No mosques found in your area. Try increasing the search radius.',
          code: 'NO_MOSQUES_FOUND'
        });
      }


      res.status(500).json({
        status: 500,
        success: false,
        error: 'An error occurred while finding nearby mosques. Please try again.',
        code: 'INTERNAL_SERVER_ERROR'
      });
    }
  }

  /**
   * Get health status of mosque service
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async getServiceHealth(req, res) {
    try {
      const health = {
        success: true,
        service: 'Mosque Finder API',
        status: 'healthy',
        timestamp: new Date().toISOString(),
        version: '1.0.0',
        features: {
          ipGeolocation: 'ip-api.com (free)',
          mosqueData: {
            openstreetmap: true,
            dataSource: 'OpenStreetMap Overpass API (free)'
          },
          publicIpDetection: 'ipify.org (free)',
          backendOnly: true
        },
        endpoints: [
          {
            path: '/api/mosques/nearby',
            method: 'GET',
            description: 'Find nearby mosques using coordinates or IP location'
          },
          {
            path: '/api/mosques/health',
            method: 'GET',
            description: 'Service health check'
          }
        ]
      };

      res.status(200).json({
        status: 200,
        success: true,
        data:health});
    } catch (error) {
      console.error('Error in getServiceHealth:', error.message);
      res.status(500).json({
        status: 500,
        success: false,
        service: 'Mosque Finder API',
        status: 'unhealthy',
        error: 'Health check failed',
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * Get API documentation/usage information
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async getApiDocs(req, res) {
    try {
      const docs = {
        success: true,
        api: 'Mosque Finder API',
        version: '1.0.0',
        description: 'Backend API for finding nearby mosques using coordinates or IP geolocation',
        baseUrl: `${req.protocol}://${req.get('host')}`,
        endpoints: {
          findNearbyMosques: {
            path: '/api/mosques/nearby',
            method: 'GET',
            description: 'Find nearby mosques using coordinates (preferred) or IP location (fallback)',
            parameters: {
              radius: {
                type: 'integer',
                default: 5000,
                min: 100,
                max: 50000,
                description: 'Search radius in meters'
              },
              latitude: {
                type: 'number',
                min: -90,
                max: 90,
                optional: true,
                description: 'Latitude coordinate (takes priority over IP location)'
              },
              longitude: {
                type: 'number',
                min: -180,
                max: 180,
                optional: true,
                description: 'Longitude coordinate (takes priority over IP location)'
              }
            },
            examples: {
              withCoordinates: '/api/mosques/nearby?latitude=40.7128&longitude=-74.0060&radius=3000',
              withIPOnly: '/api/mosques/nearby?radius=3000'
            }
          },
          health: {
            path: '/api/mosques/health',
            method: 'GET',
            description: 'Service health check and feature availability'
          },
          docs: {
            path: '/api/mosques/docs',
            method: 'GET',
            description: 'API documentation (this endpoint)'
          }
        },
        responseFormat: {
          success: 'boolean',
          location: {
            latitude: 'number',
            longitude: 'number',
            city: 'string',
            country: 'string',
            region: 'string',
            source: 'string (frontend_coordinates or ip_geolocation)'
          },
          mosques: {
            count: 'number',
            data: 'array of mosque objects',
            dataSource: 'string (openstreetmap)',
            searchRadius: 'number'
          },
          timestamp: 'ISO date string'
        },
        errorCodes: {
          INVALID_RADIUS: 'Radius parameter is invalid',
          INVALID_COORDINATES: 'Invalid coordinates provided',
          INVALID_LATITUDE: 'Latitude must be between -90 and 90',
          INVALID_LONGITUDE: 'Longitude must be between -180 and 180',
          LOCATION_DETECTION_FAILED: 'Unable to detect location from IP',
          SERVICE_UNAVAILABLE: 'External service temporarily unavailable',
          NO_MOSQUES_FOUND: 'No mosques found in the specified area',
          INTERNAL_SERVER_ERROR: 'Generic server error'
        }
      };

      res.status(200).json({
        status: 200,
        success: true,
        data: docs});
    } catch (error) {
      console.error('Error in getApiDocs:', error.message);
      res.status(500).json({
        status: 500,
        success: false,
        error: 'Unable to load API documentation'
      });
    }
  }

  /**
   * Extract client IP address from request
   * @param {Object} req - Express request object
   * @returns {string} Client IP address
   */
  getClientIP(req) {

    const forwarded = req.headers['x-forwarded-for'];
    const realIP = req.headers['x-real-ip'];
    const cfConnectingIP = req.headers['cf-connecting-ip'];

    if (forwarded) {

      return forwarded.split(',')[0].trim();
    }

    if (realIP) {
      return realIP;
    }

    if (cfConnectingIP) {
      return cfConnectingIP;
    }


    return req.connection?.remoteAddress ||
           req.socket?.remoteAddress ||
           req.ip ||
           '127.0.0.1';
  }
}

module.exports = new MosqueController();
