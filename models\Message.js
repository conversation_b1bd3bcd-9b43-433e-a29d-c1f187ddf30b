const mongoose = require('mongoose');

const MessageSchema = new mongoose.Schema({
  conversation: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Conversation',
    required: true
  },
  sender: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Step2',
    required: true
  },
  content: {
    type: String,
    required: function() {

      return this.messageType !== 'location';
    }
  },
  messageType: {
    type: String,
    enum: ['text', 'image', 'audio', 'location', 'system'],
    default: 'text'
  },
  mediaUrl: String,
  location: {
    latitude: Number,
    longitude: Number,
    address: String
  },
  readBy: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Step2'
  }],
  deliveredTo: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Step2'
  }],
  isDeleted: {
    type: Boolean,
    default: false
  }
}, { timestamps: true });

module.exports = mongoose.model('Message', MessageSchema);