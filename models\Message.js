const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const Message = sequelize.define('messages', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  conversationId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    field: 'conversation_id',
    references: {
      model: 'conversations',
      key: 'id',
    },
    onDelete: 'CASCADE',
  },
  senderId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    field: 'sender_id',
    references: {
      model: 'step2_users',
      key: 'id',
    },
    onDelete: 'CASCADE',
  },
  content: {
    type: DataTypes.TEXT,
    allowNull: true,
    validate: {
      contentRequired(value) {
        if (this.messageType !== 'location' && !value) {
          throw new Error('Content is required for non-location messages');
        }
      },
    },
  },
  messageType: {
    type: DataTypes.ENUM('text', 'image', 'audio', 'location', 'system'),
    defaultValue: 'text',
    field: 'message_type',
  },
  mediaUrl: {
    type: DataTypes.TEXT,
    allowNull: true,
    field: 'media_url',
  },
  // Location data (flattened from nested object)
  locationLatitude: {
    type: DataTypes.DECIMAL(10, 8),
    allowNull: true,
    field: 'location_latitude',
  },
  locationLongitude: {
    type: DataTypes.DECIMAL(11, 8),
    allowNull: true,
    field: 'location_longitude',
  },
  locationAddress: {
    type: DataTypes.TEXT,
    allowNull: true,
    field: 'location_address',
  },
  isDeleted: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    field: 'is_deleted',
  },
  createdAt: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
    field: 'created_at',
  },
  updatedAt: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
    field: 'updated_at',
  },
}, {
  tableName: 'messages',
  timestamps: true,
  underscored: true,
  indexes: [
    {
      fields: ['conversation_id'],
    },
    {
      fields: ['sender_id'],
    },
    {
      fields: ['message_type'],
    },
    {
      fields: ['created_at'],
    },
    {
      fields: ['is_deleted'],
    },
    {
      fields: ['conversation_id', 'created_at'],
    },
  ],
});

// Define the MessageReadStatus junction table
const MessageReadStatus = sequelize.define('message_read_status', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  messageId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    field: 'message_id',
    references: {
      model: 'messages',
      key: 'id',
    },
    onDelete: 'CASCADE',
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    field: 'user_id',
    references: {
      model: 'step2_users',
      key: 'id',
    },
    onDelete: 'CASCADE',
  },
  readAt: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
    field: 'read_at',
  },
}, {
  tableName: 'message_read_status',
  timestamps: false,
  underscored: true,
  indexes: [
    {
      fields: ['message_id'],
    },
    {
      fields: ['user_id'],
    },
    {
      unique: true,
      fields: ['message_id', 'user_id'],
    },
  ],
});

// Define the MessageDeliveryStatus junction table
const MessageDeliveryStatus = sequelize.define('message_delivery_status', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  messageId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    field: 'message_id',
    references: {
      model: 'messages',
      key: 'id',
    },
    onDelete: 'CASCADE',
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    field: 'user_id',
    references: {
      model: 'step2_users',
      key: 'id',
    },
    onDelete: 'CASCADE',
  },
  deliveredAt: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
    field: 'delivered_at',
  },
}, {
  tableName: 'message_delivery_status',
  timestamps: false,
  underscored: true,
  indexes: [
    {
      fields: ['message_id'],
    },
    {
      fields: ['user_id'],
    },
    {
      unique: true,
      fields: ['message_id', 'user_id'],
    },
  ],
});

// Define associations
Message.associate = (models) => {
  // Belongs to conversation
  Message.belongsTo(models.Conversation, {
    foreignKey: 'conversationId',
    as: 'conversation',
  });

  // Belongs to sender
  Message.belongsTo(models.Step2, {
    foreignKey: 'senderId',
    as: 'sender',
  });

  // Many-to-many relationship with Step2 users for read status
  Message.belongsToMany(models.Step2, {
    through: MessageReadStatus,
    foreignKey: 'messageId',
    otherKey: 'userId',
    as: 'readBy',
  });

  // Many-to-many relationship with Step2 users for delivery status
  Message.belongsToMany(models.Step2, {
    through: MessageDeliveryStatus,
    foreignKey: 'messageId',
    otherKey: 'userId',
    as: 'deliveredTo',
  });

  // One-to-many relationship with read status
  Message.hasMany(MessageReadStatus, {
    foreignKey: 'messageId',
    as: 'readStatus',
  });

  // One-to-many relationship with delivery status
  Message.hasMany(MessageDeliveryStatus, {
    foreignKey: 'messageId',
    as: 'deliveryStatus',
  });
};

// Virtual getter for location object (to maintain compatibility)
Message.prototype.getLocation = function() {
  if (this.locationLatitude && this.locationLongitude) {
    return {
      latitude: parseFloat(this.locationLatitude),
      longitude: parseFloat(this.locationLongitude),
      address: this.locationAddress,
    };
  }
  return null;
};

// Virtual setter for location object
Message.prototype.setLocation = function(locationData) {
  if (locationData) {
    this.locationLatitude = locationData.latitude;
    this.locationLongitude = locationData.longitude;
    this.locationAddress = locationData.address;
  }
};

// Export all related models
module.exports = {
  Message,
  MessageReadStatus,
  MessageDeliveryStatus,
};