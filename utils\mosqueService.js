const axios = require('axios');


class MosqueService {
  constructor() {
    this.ipApiUrl = 'http://ip-api.com/json';
    this.googlePlacesApiUrl = 'https://maps.googleapis.com/maps/api/place/nearbysearch/json';
    this.overpassApiUrl = 'https://overpass-api.de/api/interpreter';
  }

  /**
   * Get user location from IP address using ip-api.com (free service)
   * @param {string} ip - Client IP address
   * @returns {Promise<Object>} Location data with lat, lng, city, country
   */
  async getLocationFromIP(ip) {
    try {
      console.log(`Attempting to get location for IP: ${ip}`);


      if (ip === '127.0.0.1' || ip === 'localhost') {
        console.log('Detected localhost IP, using default location for development');

        return {
          lat: 40.7128,
          lng: -74.0060,
          city: 'New York',
          country: 'United States',
          regionName: 'New York',
          zip: '10001',
          status: 'success',
          isLocalhost: true
        };
      }


      if (ip === '::1') {
        console.log('Detected IPv6 localhost, attempting to get public IP');
        try {

          const publicIpResponse = await axios.get('https://api.ipify.org?format=json', {
            timeout: 5000
          });
          const publicIp = publicIpResponse.data.ip;
          console.log(`Found public IP: ${publicIp}`);

          if (publicIp && publicIp !== ip) {
            return await this.getLocationFromIP(publicIp);
          }
        } catch (publicIpError) {
          console.warn('Could not get public IP, falling back to default location');
        }


        return {
          lat: 40.7128,
          lng: -74.0060,
          city: 'New York',
          country: 'United States',
          regionName: 'New York',
          zip: '10001',
          status: 'success',
          isLocalhost: true
        };
      }

      const response = await axios.get(`${this.ipApiUrl}/${ip}`, {
        timeout: 10000,
        params: {
          fields: 'status,message,country,regionName,city,zip,lat,lon,query,isp,org'
        }
      });

      const data = response.data;
      console.log('IP API Response:', data);

      if (data.status === 'fail') {
        throw new Error(data.message || 'Failed to get location from IP');
      }

      return {
        lat: data.lat,
        lng: data.lon,
        city: data.city,
        country: data.country,
        regionName: data.regionName,
        zip: data.zip,
        isp: data.isp,
        org: data.org,
        status: 'success',
        isLocalhost: false
      };
    } catch (error) {
      console.error('Error getting location from IP:', error.message);
      throw new Error('Unable to detect location from IP address');
    }
  }

  /**
   * Find nearby mosques using Google Places API
   * @param {number} lat - Latitude
   * @param {number} lng - Longitude
   * @param {number} radius - Search radius in meters (default: 5000)
   * @returns {Promise<Array>} Array of nearby mosques
   */
  async findNearbyMosquesGoogle(lat, lng, radius = 5000) {
    try {
      const apiKey = process.env.GOOGLE_PLACES_API_KEY;
      if (!apiKey) {
        throw new Error('Google Places API key not configured');
      }

      const response = await axios.get(this.googlePlacesApiUrl, {
        timeout: 15000,
        params: {
          location: `${lat},${lng}`,
          radius: radius,
          type: 'mosque',
          key: apiKey
        }
      });

      const places = response.data.results || [];

      return places.map(place => ({
        name: place.name,
        address: place.vicinity || place.formatted_address || 'Address not available',
        coordinates: {
          lat: place.geometry.location.lat,
          lng: place.geometry.location.lng
        },
        rating: place.rating || null,
        placeId: place.place_id,
        openNow: place.opening_hours?.open_now || null,
        priceLevel: place.price_level || null
      }));
    } catch (error) {
      console.error('Error finding mosques with Google Places:', error.message);
      throw new Error('Unable to find nearby mosques using Google Places API');
    }
  }

  /**
   * Find nearby mosques using OpenStreetMap Overpass API (free alternative)
   * @param {number} lat - Latitude
   * @param {number} lng - Longitude
   * @param {number} radius - Search radius in meters (default: 5000)
   * @returns {Promise<Array>} Array of nearby mosques
   */
  async findNearbyMosquesOSM(lat, lng, radius = 5000) {
    try {
      const overpassQuery = `
        [out:json][timeout:25];
        (
          node["amenity"="place_of_worship"]["religion"="muslim"](around:${radius},${lat},${lng});
          way["amenity"="place_of_worship"]["religion"="muslim"](around:${radius},${lat},${lng});
          relation["amenity"="place_of_worship"]["religion"="muslim"](around:${radius},${lat},${lng});
        );
        out center;
      `;

      const response = await axios.post(this.overpassApiUrl, overpassQuery, {
        timeout: 30000,
        headers: {
          'Content-Type': 'text/plain'
        }
      });

      const data = response.data;
      const elements = data.elements || [];

      return elements.map(element => {
        const tags = element.tags || {};
        const coordinates = element.center || { lat: element.lat, lon: element.lon };

        return {
          name: tags.name || tags['name:en'] || tags['name:ar'] || 'Unnamed Mosque',
          address: this.formatOSMAddress(tags),
          coordinates: {
            lat: coordinates.lat,
            lng: coordinates.lon
          },
          denomination: tags.denomination || null,
          website: tags.website || null,
          phone: tags.phone || null,
          osmId: element.id,
          osmType: element.type
        };
      });
    } catch (error) {
      console.error('Error finding mosques with OSM:', error.message);
      throw new Error('Unable to find nearby mosques using OpenStreetMap');
    }
  }

  /**
   * Format OSM address from tags
   * @param {Object} tags - OSM tags
   * @returns {string} Formatted address
   */
  formatOSMAddress(tags) {
    const addressParts = [];

    if (tags['addr:housenumber']) addressParts.push(tags['addr:housenumber']);
    if (tags['addr:street']) addressParts.push(tags['addr:street']);
    if (tags['addr:city']) addressParts.push(tags['addr:city']);
    if (tags['addr:state']) addressParts.push(tags['addr:state']);
    if (tags['addr:postcode']) addressParts.push(tags['addr:postcode']);
    if (tags['addr:country']) addressParts.push(tags['addr:country']);

    return addressParts.length > 0 ? addressParts.join(', ') : 'Address not available';
  }





  /**
   * Main method to find nearby mosques using free services only
   * @param {string} ip - Client IP address (used when coordinates are not provided)
   * @param {Object} options - Search options
   * @param {number} options.radius - Search radius in meters (default: 5000)
   * @param {number} options.latitude - Optional latitude coordinate from frontend
   * @param {number} options.longitude - Optional longitude coordinate from frontend
   * @returns {Promise<Object>} Complete response with location and mosques
   */
  async findNearbyMosques(ip, options = {}) {
    const { radius = 5000, latitude, longitude } = options;

    try {
      let location;
      let locationSource;


      if (latitude !== undefined && longitude !== undefined) {


        location = {
          lat: parseFloat(latitude),
          lng: parseFloat(longitude),
          city: 'Location from coordinates',
          country: 'Unknown',
          regionName: 'Unknown',
          isLocalhost: false
        };
        locationSource = 'frontend_coordinates';
      } else {


        location = await this.getLocationFromIP(ip);
        locationSource = 'ip_geolocation';
      }


      const mosques = await this.findNearbyMosquesOSM(location.lat, location.lng, radius);
      const dataSource = 'openstreetmap';

      return {
        success: true,
        location: {
          latitude: location.lat,
          longitude: location.lng,
          city: location.city,
          country: location.country,
          region: location.regionName,
          isLocalhost: location.isLocalhost || false,
          source: locationSource
        },
        mosques: {
          count: mosques.length,
          data: mosques,
          dataSource,
          searchRadius: radius
        },
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('Error in findNearbyMosques:', error.message);
      throw error;
    }
  }
}

module.exports = new MosqueService();
