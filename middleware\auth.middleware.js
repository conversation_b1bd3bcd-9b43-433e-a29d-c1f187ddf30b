const jwt = require('jsonwebtoken');
const Step2 = require('../models/Step2.Model');

const authMiddleware = async (req, res, next) => {
  try {
    const authHeader = req.header('Authorization');

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        status: 401,
        success: false,
        message: 'No token provided or invalid format'
      });
    }

    const token = authHeader.replace('Bearer ', '').trim();
    const decoded = jwt.verify(token, process.env.JWT_SECRET);

    const user = await Step2.findById(decoded.userId);

    if (!user) {
      return res.status(401).json({
        status: 401,
        success: false,
        message: 'User not found or token invalid'
      });
    }

    req.user = user;
    req.token = token;
    next();

  } catch (error) {
    return res.status(401).json({
      status: 401,
      success: false,
      message: 'Please authenticate.',
      error: error.message
    });
  }
};

module.exports = authMiddleware;
