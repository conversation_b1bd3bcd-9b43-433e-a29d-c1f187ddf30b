const sequelize = require('../config/database');

// Import all models
const Step1 = require('./Step1.Model');
const Step2 = require('./Step2.Model');
const { Conversation, ConversationParticipant, ConversationUnreadCount } = require('./Conversation');
const { Message, MessageReadStatus, MessageDeliveryStatus } = require('./Message');
const { Group, GroupMember } = require('./Group');

// Define all associations
const models = {
  Step1,
  Step2,
  Conversation,
  ConversationParticipant,
  ConversationUnreadCount,
  Message,
  MessageReadStatus,
  MessageDeliveryStatus,
  Group,
  GroupMember,
};

// Set up associations
Object.keys(models).forEach(modelName => {
  if (models[modelName].associate) {
    models[modelName].associate(models);
  }
});

// Define Step1 -> Step2 relationship
Step1.hasMany(Step2, {
  foreignKey: 'step1Id',
  as: 'step2Users',
});

Step2.belongsTo(Step1, {
  foreignKey: 'step1Id',
  as: 'step1User',
});

// Define Conversation relationships
Conversation.belongsToMany(Step2, {
  through: ConversationParticipant,
  foreignKey: 'conversationId',
  otherKey: 'userId',
  as: 'participants',
});

Step2.belongsToMany(Conversation, {
  through: ConversationParticipant,
  foreignKey: 'userId',
  otherKey: 'conversationId',
  as: 'conversations',
});

Conversation.hasMany(ConversationUnreadCount, {
  foreignKey: 'conversationId',
  as: 'unreadCounts',
});

ConversationUnreadCount.belongsTo(Conversation, {
  foreignKey: 'conversationId',
  as: 'conversation',
});

ConversationUnreadCount.belongsTo(Step2, {
  foreignKey: 'userId',
  as: 'user',
});

// Define Message relationships
Message.belongsTo(Conversation, {
  foreignKey: 'conversationId',
  as: 'conversation',
});

Conversation.hasMany(Message, {
  foreignKey: 'conversationId',
  as: 'messages',
});

Message.belongsTo(Step2, {
  foreignKey: 'senderId',
  as: 'sender',
});

Step2.hasMany(Message, {
  foreignKey: 'senderId',
  as: 'sentMessages',
});

// Message read/delivery status relationships
Message.belongsToMany(Step2, {
  through: MessageReadStatus,
  foreignKey: 'messageId',
  otherKey: 'userId',
  as: 'readBy',
});

Message.belongsToMany(Step2, {
  through: MessageDeliveryStatus,
  foreignKey: 'messageId',
  otherKey: 'userId',
  as: 'deliveredTo',
});

Step2.belongsToMany(Message, {
  through: MessageReadStatus,
  foreignKey: 'userId',
  otherKey: 'messageId',
  as: 'readMessages',
});

Step2.belongsToMany(Message, {
  through: MessageDeliveryStatus,
  foreignKey: 'userId',
  otherKey: 'messageId',
  as: 'deliveredMessages',
});

// Last message relationship
Conversation.belongsTo(Message, {
  foreignKey: 'lastMessageId',
  as: 'lastMessage',
});

// Group relationships
Group.belongsTo(Conversation, {
  foreignKey: 'conversationId',
  as: 'conversation',
});

Conversation.hasOne(Group, {
  foreignKey: 'conversationId',
  as: 'group',
});

Group.belongsTo(Step2, {
  foreignKey: 'adminId',
  as: 'admin',
});

Step2.hasMany(Group, {
  foreignKey: 'adminId',
  as: 'adminGroups',
});

// Group member relationships
Group.belongsToMany(Step2, {
  through: GroupMember,
  foreignKey: 'groupId',
  otherKey: 'userId',
  as: 'members',
});

Step2.belongsToMany(Group, {
  through: GroupMember,
  foreignKey: 'userId',
  otherKey: 'groupId',
  as: 'groups',
});

Group.hasMany(GroupMember, {
  foreignKey: 'groupId',
  as: 'memberDetails',
});

GroupMember.belongsTo(Group, {
  foreignKey: 'groupId',
  as: 'group',
});

GroupMember.belongsTo(Step2, {
  foreignKey: 'userId',
  as: 'user',
});

Step2.hasMany(GroupMember, {
  foreignKey: 'userId',
  as: 'groupMemberships',
});

// Export sequelize instance and models
module.exports = {
  sequelize,
  ...models,
};
