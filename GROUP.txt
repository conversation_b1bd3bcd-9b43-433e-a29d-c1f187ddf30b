# Group Chat API Documentation

A comprehensive real-time group chat system built with Node.js, Express, Socket.IO, and MongoDB. This API enables WhatsApp-like group functionality with persistent message history and real-time messaging capabilities.

## Features

- **Real-time Group Messaging**: Instant message delivery using Socket.IO
- **Group Management**: Create, join, leave groups with admin controls
- **Persistent Chat History**: All messages stored in MongoDB
- **User Management**: Add/remove members, admin privileges
- **Message Status**: Delivery and read receipts
- **Typing Indicators**: Real-time typing status in groups
- **Mobile-Ready**: Backend-only API designed for mobile app integration

## API Endpoints

### Group Management

#### Create Group
```http
POST /api/groups
```

**Request Body:**
```json
{
  "name": "My Group",
  "description": "Group description",
  "image": "https://example.com/image.jpg",
  "adminId": "user_id_here",
  "memberIds": ["user1_id", "user2_id"]
}
```

**Response:**
```json
{
  "status": 201,
  "success": true,
  "message": "Group created successfully",
  "data": {
    "_id": "group_id",
    "name": "My Group",
    "description": "Group description",
    "admin": {...},
    "members": [...],
    "memberCount": 3,
    "settings": {
      "allowMembersToAddOthers": false,
      "onlyAdminsCanSend": false,
      "maxMembers": 256
    }
  }
}
```

#### Get User Groups
```http
GET /api/groups/user/{userId}?page=1&limit=20
```

#### Get Group Details
```http
GET /api/groups/{groupId}?userId={userId}
```

#### Join Group
```http
POST /api/groups/{groupId}/join
```

**Request Body:**
```json
{
  "userId": "user_id_here"
}
```

#### Leave Group
```http
POST /api/groups/{groupId}/leave
```

**Request Body:**
```json
{
  "userId": "user_id_here"
}
```

### Messaging

#### Get Group Messages
```http
GET /api/groups/{groupId}/messages?userId={userId}&page=1&limit=50
```

#### Send Group Message
```http
POST /api/groups/{groupId}/messages
```

**Request Body:**
```json
{
  "senderId": "user_id_here",
  "content": "Hello everyone!",
  "messageType": "text"
}
```

## Socket.IO Events

### Client to Server Events

#### Connect to Chat
```javascript
socket.emit('join', {
  userId: 'user_id_here'
});
```

#### Join Group Room
```javascript
socket.emit('join-group', {
  groupId: 'group_id_here',
  userId: 'user_id_here'
});
```

#### Leave Group Room
```javascript
socket.emit('leave-group', {
  groupId: 'group_id_here',
  userId: 'user_id_here'
});
```

#### Send Group Message
```javascript
socket.emit('send-group-message', {
  groupId: 'group_id_here',
  senderId: 'user_id_here',
  content: 'Hello everyone!',
  messageType: 'text'
});
```

#### Group Typing Indicator
```javascript
socket.emit('group-typing', {
  groupId: 'group_id_here',
  userId: 'user_id_here',
  isTyping: true
});
```

### Server to Client Events

#### Connection Confirmed
```javascript
socket.on('connected', (data) => {
  console.log('Connected to chat:', data);
});
```

#### Group Room Joined
```javascript
socket.on('group-joined', (data) => {
  console.log('Joined group room:', data);
});
```

#### Group Room Left
```javascript
socket.on('group-left', (data) => {
  console.log('Left group room:', data);
});
```

#### Receive Group Message
```javascript
socket.on('receive-message', (data) => {
  console.log('New group message:', data);
  // data contains: message, groupId, groupName
});
```

#### User Joined Group Room
```javascript
socket.on('user-joined-room', (data) => {
  console.log('User joined group room:', data);
});
```

#### User Left Group Room
```javascript
socket.on('user-left-room', (data) => {
  console.log('User left group room:', data);
});
```

#### Message Delivered
```javascript
socket.on('message-delivered', (data) => {
  console.log('Message delivered:', data);
});
```

#### User Typing
```javascript
socket.on('user-typing', (data) => {
  console.log('User typing status:', data);
});
```

#### Error Handling
```javascript
socket.on('error', (error) => {
  console.error('Socket error:', error);
});
```

## Data Models

### Group Schema
```javascript
{
  name: String,           // Group name (required, max 100 chars)
  description: String,    // Group description (max 500 chars)
  image: String,          // Group image URL
  admin: ObjectId,        // Main admin user ID
  admins: [ObjectId],     // Array of admin user IDs
  members: [{
    user: ObjectId,       // User ID
    role: String,         // 'admin' or 'member'
    joinedAt: Date        // When user joined
  }],
  settings: {
    allowMembersToAddOthers: Boolean,  // Can members add others
    onlyAdminsCanSend: Boolean,        // Only admins can send messages
    maxMembers: Number                 // Maximum group size (default: 256)
  },
  conversation: ObjectId, // Associated conversation ID
  isActive: Boolean,      // Group status
  lastActivity: Date,     // Last group activity
  memberCount: Number     // Virtual field for member count
}
```

### Message Schema
```javascript
{
  conversation: ObjectId,    // Conversation ID
  sender: ObjectId,         // Sender user ID
  content: String,          // Message content
  messageType: String,      // 'text', 'image', 'audio', 'location', 'system'
  readBy: [ObjectId],       // Users who read the message
  deliveredTo: [ObjectId],  // Users who received the message
  isDeleted: Boolean,       // Soft delete flag
  createdAt: Date,          // Message timestamp
  updatedAt: Date
}
```

## Usage Example

### 1. Start the Server
```bash
npm start
```

### 2. Create a Group
```javascript
const response = await fetch('/api/groups', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    name: 'My Chat Group',
    adminId: 'admin_user_id',
    memberIds: ['user1_id', 'user2_id']
  })
});
```

### 3. Connect to Real-time Chat
```javascript
const socket = io('http://localhost:5000');

// Connect user
socket.emit('join', { userId: 'your_user_id' });

// Join group room
socket.emit('join-group', {
  groupId: 'group_id',
  userId: 'your_user_id'
});

// Listen for messages
socket.on('receive-message', (data) => {
  console.log('New message:', data.message);
});

// Send message
socket.emit('send-group-message', {
  groupId: 'group_id',
  senderId: 'your_user_id',
  content: 'Hello everyone!'
});
```

## Error Handling

All API endpoints return standardized error responses:

```json
{
  "status": 400,
  "success": false,
  "message": "Error description"
}
```

Common HTTP status codes:
- `200`: Success
- `201`: Created
- `400`: Bad Request
- `403`: Forbidden
- `404`: Not Found
- `500`: Internal Server Error

## Security Features

- Input validation and sanitization
- Member verification for all group operations
- Admin permission checks
- Rate limiting (configurable)
- XSS protection
- CORS configuration

## Performance Considerations

- Database indexing on frequently queried fields
- Pagination for message history
- Efficient Socket.IO room management
- Connection pooling for MongoDB
- Memory-efficient user session management

## Next Steps

To extend this group chat system, you could add:

1. **File Sharing**: Support for images, documents, and media
2. **Message Reactions**: Emoji reactions to messages
3. **Message Threading**: Reply to specific messages
4. **Group Settings**: More granular permissions and settings
5. **Push Notifications**: Mobile push notification integration
6. **Message Encryption**: End-to-end encryption support
7. **Group Analytics**: Message statistics and insights
8. **Moderation Tools**: Message deletion, user muting, etc.

## API Documentation

Full interactive API documentation is available at:
- Swagger UI: `http://localhost:5000/api-docs`
- JSON Spec: `http://localhost:5000/swagger.json`
