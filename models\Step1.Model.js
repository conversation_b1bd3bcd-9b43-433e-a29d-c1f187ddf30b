
const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const Step1 = sequelize.define('step1_users', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  language: {
    type: DataTypes.STRING(50),
    allowNull: false,
  },
  country: {
    type: DataTypes.STRING(100),
    allowNull: false,
  },
  phoneNumber: {
    type: DataTypes.STRING(20),
    allowNull: false,
    field: 'phone_number',
  },
  email: {
    type: DataTypes.STRING(255),
    allowNull: false,
    unique: true,
    validate: {
      isEmail: true,
    },
  },
  otp: {
    type: DataTypes.STRING(10),
    allowNull: true,
  },
  otpCreatedAt: {
    type: DataTypes.DATE,
    allowNull: true,
    defaultValue: DataTypes.NOW,
    field: 'otp_created_at',
  },
  createdAt: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
    field: 'created_at',
  },
  updatedAt: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
    field: 'updated_at',
  },
}, {
  tableName: 'step1_users',
  timestamps: true,
  underscored: true,
  indexes: [
    {
      fields: ['email'],
    },
    {
      fields: ['phone_number'],
    },
    {
      fields: ['otp_created_at'],
    },
  ],
});

module.exports = Step1;

