
const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
const dotenv = require('dotenv');
const connectDB = require('./config/db.js');
const step1Routes = require('./routes/step1.Routes.js');
const step2Routes = require('./routes/step2.Routes.js');
const chatRoutes = require('./routes/chat.Routes.js');
const groupRoutes = require('./routes/group.Routes.js');
const mosqueRoutes = require('./routes/mosque.Routes.js');
const loginRoutes = require('./routes/login.Routes.js');
const verifyOtp = require('./controllers//verify.Otp.Controller.js');
const initializeChatSocket = require('./socket/chatSocket.js');
const errorMiddleware = require('./middleware/error.middleware.js');



const swaggerSpec = require('./appSwagger.js');
const swaggerHTML = require('./swaggerCustomUIHTML.js');

dotenv.config();

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"]
  }
});

const PORT = process.env.PORT || 5000;


connectDB();


const chatSocketHelpers = initializeChatSocket(io);


app.use(cors());
app.use(express.json());

app.get('/swagger.json',(req,res)=>{
    res.setHeader('Content-Type', 'application/json');
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.send(swaggerSpec);
})


app.get('/api-docs',(req,res)=>{
    res.setHeader('Content-Type', 'text/html');
    res.send(swaggerHTML);
})


app.use('/api/step1', step1Routes);
app.use('/api/step2', step2Routes);
app.use('/api/chat', chatRoutes);
app.use('/api/groups', groupRoutes);
app.use('/api/mosques', mosqueRoutes);
app.use('/api/user', loginRoutes);

app.post('/api/verify-otp', verifyOtp);


app.get('/health', (req, res) => {
  res.status(200).json({
    success: true,
    message: 'Server is running',
    timestamp: new Date().toISOString()
  });
});

app.use(errorMiddleware);

server.listen(PORT, () => {
  console.log(`Server running on http://localhost:${PORT}`);
  console.log(`Socket.IO enabled for real-time chat`);
});
