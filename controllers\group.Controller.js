const {
  Conversation,
  ConversationParticipant,
  ConversationUnreadCount,
  Message,
  MessageReadStatus,
  MessageDeliveryStatus,
  Step2,
  Group,
  GroupMember
} = require('../models');


const createGroup = async (req, res) => {
  try {
    const { name, description, image, adminId, memberIds = [] } = req.body;

    if (!name || !adminId) {
      return res.status(400).json({
        status: 400,
        success: false,
        message: 'Group name and admin ID are required'
      });
    }

    // Check if admin exists
    const admin = await Step2.findByPk(adminId);
    if (!admin) {
      return res.status(404).json({
        status: 404,
        success: false,
        message: 'Admin user not found'
      });
    }

    // Get unique member IDs including admin
    const uniqueMemberIds = [...new Set([adminId, ...memberIds])];
    const members = await Step2.findAll({
      where: {
        id: uniqueMemberIds
      }
    });

    if (members.length !== uniqueMemberIds.length) {
      return res.status(404).json({
        status: 404,
        success: false,
        message: 'One or more users not found'
      });
    }

    // Create conversation
    const conversation = await Conversation.create({
      isGroup: true
    });

    // Add participants to conversation
    await ConversationParticipant.bulkCreate(
      uniqueMemberIds.map(userId => ({
        conversationId: conversation.id,
        userId: userId
      }))
    );

    // Add unread counts for all participants
    await ConversationUnreadCount.bulkCreate(
      uniqueMemberIds.map(userId => ({
        conversationId: conversation.id,
        userId: userId,
        count: 0
      }))
    );

    // Create group
    const group = await Group.create({
      conversationId: conversation.id,
      name: name.trim(),
      description: description?.trim(),
      image,
      adminId: adminId
    });

    // Add group members
    await GroupMember.bulkCreate(
      uniqueMemberIds.map(userId => ({
        groupId: group.id,
        userId: userId,
        role: userId === parseInt(adminId) ? 'admin' : 'member',
        joinedAt: new Date()
      }))
    );

    // Load group with all associations
    const groupWithAssociations = await Group.findByPk(group.id, {
      include: [
        {
          model: Step2,
          as: 'admin',
          attributes: ['id', 'username', 'image']
        },
        {
          model: Step2,
          as: 'members',
          attributes: ['id', 'username', 'image'],
          through: {
            attributes: ['role', 'joinedAt']
          }
        },
        {
          model: Conversation,
          as: 'conversation'
        }
      ]
    });


    const systemMessage = new Message({
      conversation: conversation._id,
      sender: adminId,
      content: `${admin.username} created the group "${name}"`,
      messageType: 'system'
    });
    await systemMessage.save();

    res.status(201).json({
      status: 201,
      success: true,
      message: 'Group created successfully',
      data: group
    });
  } catch (error) {
    console.error('Error creating group:', error);
    res.status(500).json({
      status: 500,
      success: false,
      message: 'Internal server error'
    });
  }
};


const getUserGroups = async (req, res) => {
  try {
    const { userId } = req.params;
    const { page = 1, limit = 20 } = req.query;

    if (!userId) {
      return res.status(400).json({
        status: 400,
        success: false,
        message: 'User ID is required'
      });
    }

    const skip = (page - 1) * limit;

    const groups = await Group.find({
      'members.user': userId,
      isActive: true
    })
    .populate('admin', 'username image')
    .populate('members.user', 'username image')
    .populate({
      path: 'conversation',
      populate: {
        path: 'lastMessage',
        populate: {
          path: 'sender',
          select: 'username'
        }
      }
    })
    .sort({ lastActivity: -1 })
    .skip(skip)
    .limit(parseInt(limit));

    const totalGroups = await Group.countDocuments({
      'members.user': userId,
      isActive: true
    });

    res.status(200).json({
      status: 200,
      success: true,
      data: {
        groups,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(totalGroups / limit),
          totalGroups,
          hasMore: skip + groups.length < totalGroups
        }
      }
    });
  } catch (error) {
    console.error('Error fetching user groups:', error);
    res.status(500).json({
      status: 500,
      success: false,
      message: 'Internal server error'
    });
  }
};


const getGroupById = async (req, res) => {
  try {
    const { groupId } = req.params;
    const { userId } = req.query;

    if (!groupId) {
      return res.status(400).json({
        status: 400,
        success: false,
        message: 'Group ID is required'
      });
    }

    const group = await Group.findById(groupId)
      .populate('admin', 'username image')
      .populate('admins', 'username image')
      .populate('members.user', 'username image isOnline lastSeen')
      .populate('conversation');

    if (!group) {
      return res.status(404).json({
        status: 404,
        success: false,
        message: 'Group not found'
      });
    }


    if (userId && !group.isUserMember(userId)) {
      return res.status(403).json({
        status: 403,
        success: false,
        message: 'Access denied. You are not a member of this group'
      });
    }

    res.status(200).json({
      status: 200,
      success: true,
      data: group
    });
  } catch (error) {
    console.error('Error fetching group:', error);
    res.status(500).json({
      status: 500,
      success: false,
      message: 'Internal server error'
    });
  }
};


const joinGroup = async (req, res) => {
  try {
    const { groupId } = req.params;
    const { userId } = req.body;

    if (!groupId || !userId) {
      return res.status(400).json({
        status: 400,
        success: false,
        message: 'Group ID and user ID are required'
      });
    }


    const user = await Step2.findById(userId);
    if (!user) {
      return res.status(404).json({
        status: 404,
        success: false,
        message: 'User not found'
      });
    }


    const group = await Group.findById(groupId);
    if (!group || !group.isActive) {
      return res.status(404).json({
        status: 404,
        success: false,
        message: 'Group not found or inactive'
      });
    }


    if (group.isUserMember(userId)) {
      return res.status(400).json({
        status: 400,
        success: false,
        message: 'User is already a member of this group'
      });
    }


    if (group.members.length >= group.settings.maxMembers) {
      return res.status(400).json({
        status: 400,
        success: false,
        message: 'Group has reached maximum member limit'
      });
    }


    group.members.push({
      user: userId,
      role: 'member',
      joinedAt: new Date()
    });


    const conversation = await Conversation.findById(group.conversation);
    if (!conversation.participants.includes(userId)) {
      conversation.participants.push(userId);
      conversation.unreadCounts.push({
        user: userId,
        count: 0
      });
      await conversation.save();
    }

    group.lastActivity = new Date();
    await group.save();


    const systemMessage = new Message({
      conversation: group.conversation,
      sender: userId,
      content: `${user.username} joined the group`,
      messageType: 'system'
    });
    await systemMessage.save();

    await group.populate([
      { path: 'admin', select: 'username image' },
      { path: 'members.user', select: 'username image' }
    ]);

    res.status(200).json({
      status: 200,
      success: true,
      message: 'Successfully joined the group',
      data: group
    });
  } catch (error) {
    console.error('Error joining group:', error);
    res.status(500).json({
      status: 500,
      success: false,
      message: 'Internal server error'
    });
  }
};


const leaveGroup = async (req, res) => {
  try {
    const { groupId } = req.params;
    const { userId } = req.body;

    if (!groupId || !userId) {
      return res.status(400).json({
        status: 400,
        success: false,
        message: 'Group ID and user ID are required'
      });
    }


    const group = await Group.findById(groupId);
    if (!group) {
      return res.status(404).json({
        status: 404,
        success: false,
        message: 'Group not found'
      });
    }


    if (!group.isUserMember(userId)) {
      return res.status(400).json({
        status: 400,
        success: false,
        message: 'User is not a member of this group'
      });
    }


    if (group.admin.toString() === userId) {
      return res.status(400).json({
        status: 400,
        success: false,
        message: 'Group admin cannot leave the group. Transfer admin rights first or delete the group'
      });
    }

    const user = await Step2.findById(userId);


    group.members = group.members.filter(member =>
      member.user.toString() !== userId
    );


    group.admins = group.admins.filter(adminId =>
      adminId.toString() !== userId
    );


    const conversation = await Conversation.findById(group.conversation);
    conversation.participants = conversation.participants.filter(participantId =>
      participantId.toString() !== userId
    );
    conversation.unreadCounts = conversation.unreadCounts.filter(unreadCount =>
      unreadCount.user.toString() !== userId
    );
    await conversation.save();

    group.lastActivity = new Date();
    await group.save();


    const systemMessage = new Message({
      conversation: group.conversation,
      sender: userId,
      content: `${user.username} left the group`,
      messageType: 'system'
    });
    await systemMessage.save();

    res.status(200).json({
      status: 200,
      success: true,
      message: 'Successfully left the group'
    });
  } catch (error) {
    console.error('Error leaving group:', error);
    res.status(500).json({
      status: 500,
      success: false,
      message: 'Internal server error'
    });
  }
};


const getGroupMessages = async (req, res) => {
  try {
    const { groupId } = req.params;
    const { userId, page = 1, limit = 50 } = req.query;

    if (!groupId) {
      return res.status(400).json({
        status: 400,
        success: false,
        message: 'Group ID is required'
      });
    }


    const group = await Group.findById(groupId);
    if (!group) {
      return res.status(404).json({
        status: 404,
        success: false,
        message: 'Group not found'
      });
    }

    if (userId && !group.isUserMember(userId)) {
      return res.status(403).json({
        status: 403,
        success: false,
        message: 'Access denied. You are not a member of this group'
      });
    }

    const skip = (page - 1) * limit;

    const messages = await Message.find({
      conversation: group.conversation,
      isDeleted: false
    })
    .populate('sender', 'username image')
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(parseInt(limit));

    const totalMessages = await Message.countDocuments({
      conversation: group.conversation,
      isDeleted: false
    });

    res.status(200).json({
      status: 200,
      success: true,
      data: {
        messages: messages.reverse(),
        group: {
          _id: group._id,
          name: group.name,
          image: group.image
        },
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(totalMessages / limit),
          totalMessages,
          hasMore: skip + messages.length < totalMessages
        }
      }
    });
  } catch (error) {
    console.error('Error fetching group messages:', error);
    res.status(500).json({
      status: 500,
      success: false,
      message: 'Internal server error'
    });
  }
};


const sendGroupMessage = async (req, res) => {
  try {
    const { groupId } = req.params;
    const { senderId, content, messageType = 'text' } = req.body;

    if (!groupId || !senderId || !content) {
      return res.status(400).json({
        status: 400,
        success: false,
        message: 'Group ID, sender ID, and content are required'
      });
    }


    const group = await Group.findById(groupId);
    if (!group || !group.isActive) {
      return res.status(404).json({
        status: 404,
        success: false,
        message: 'Group not found or inactive'
      });
    }

    if (!group.isUserMember(senderId)) {
      return res.status(403).json({
        status: 403,
        success: false,
        message: 'Access denied. You are not a member of this group'
      });
    }


    if (group.settings.onlyAdminsCanSend && !group.isUserAdmin(senderId)) {
      return res.status(403).json({
        status: 403,
        success: false,
        message: 'Only group admins can send messages in this group'
      });
    }


    const message = new Message({
      conversation: group.conversation,
      sender: senderId,
      content,
      messageType,
      deliveredTo: [senderId]
    });

    await message.save();
    await message.populate('sender', 'username image');


    const conversation = await Conversation.findById(group.conversation);
    conversation.lastMessage = message._id;


    conversation.unreadCounts.forEach(unreadCount => {
      if (unreadCount.user.toString() !== senderId) {
        unreadCount.count += 1;
      }
    });

    await conversation.save();


    group.lastActivity = new Date();
    await group.save();

    res.status(201).json({
      status: 201,
      success: true,
      data: message
    });
  } catch (error) {
    console.error('Error sending group message:', error);
    res.status(500).json({
      status: 500,
      success: false,
      message: 'Internal server error'
    });
  }
};

module.exports = {
  createGroup,
  getUserGroups,
  getGroupById,
  joinGroup,
  leaveGroup,
  getGroupMessages,
  sendGroupMessage
};
