const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const Conversation = sequelize.define('conversations', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  isGroup: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    field: 'is_group',
  },
  lastMessageId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'last_message_id',
    references: {
      model: 'messages',
      key: 'id',
    },
    onDelete: 'SET NULL',
  },
  createdAt: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
    field: 'created_at',
  },
  updatedAt: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
    field: 'updated_at',
  },
}, {
  tableName: 'conversations',
  timestamps: true,
  underscored: true,
  indexes: [
    {
      fields: ['is_group'],
    },
    {
      fields: ['updated_at'],
    },
  ],
});

// Define the ConversationParticipant junction table
const ConversationParticipant = sequelize.define('conversation_participants', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  conversationId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    field: 'conversation_id',
    references: {
      model: 'conversations',
      key: 'id',
    },
    onDelete: 'CASCADE',
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    field: 'user_id',
    references: {
      model: 'step2_users',
      key: 'id',
    },
    onDelete: 'CASCADE',
  },
  joinedAt: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
    field: 'joined_at',
  },
}, {
  tableName: 'conversation_participants',
  timestamps: false,
  underscored: true,
  indexes: [
    {
      fields: ['conversation_id'],
    },
    {
      fields: ['user_id'],
    },
    {
      unique: true,
      fields: ['conversation_id', 'user_id'],
    },
  ],
});

// Define the ConversationUnreadCount table
const ConversationUnreadCount = sequelize.define('conversation_unread_counts', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  conversationId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    field: 'conversation_id',
    references: {
      model: 'conversations',
      key: 'id',
    },
    onDelete: 'CASCADE',
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    field: 'user_id',
    references: {
      model: 'step2_users',
      key: 'id',
    },
    onDelete: 'CASCADE',
  },
  count: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
  },
}, {
  tableName: 'conversation_unread_counts',
  timestamps: false,
  underscored: true,
  indexes: [
    {
      fields: ['conversation_id'],
    },
    {
      fields: ['user_id'],
    },
    {
      unique: true,
      fields: ['conversation_id', 'user_id'],
    },
  ],
});

// Define associations
Conversation.associate = (models) => {
  // Many-to-many relationship with Step2 users through ConversationParticipant
  Conversation.belongsToMany(models.Step2, {
    through: ConversationParticipant,
    foreignKey: 'conversationId',
    otherKey: 'userId',
    as: 'participants',
  });

  // One-to-many relationship with ConversationUnreadCount
  Conversation.hasMany(ConversationUnreadCount, {
    foreignKey: 'conversationId',
    as: 'unreadCounts',
  });

  // Belongs to last message
  Conversation.belongsTo(models.Message, {
    foreignKey: 'lastMessageId',
    as: 'lastMessage',
  });

  // One-to-many relationship with messages
  Conversation.hasMany(models.Message, {
    foreignKey: 'conversationId',
    as: 'messages',
  });
};

// Export all related models
module.exports = {
  Conversation,
  ConversationParticipant,
  ConversationUnreadCount,
};