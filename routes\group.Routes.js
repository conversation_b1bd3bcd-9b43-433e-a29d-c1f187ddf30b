const express = require('express');
const router = express.Router();
const {
  createGroup,
  getUserGroups,
  getGroupById,
  joinGroup,
  leaveGroup,
  getGroupMessages,
  sendGroupMessage
} = require('../controllers/group.Controller');

/**
 * @swagger
 * components:
 *   schemas:
 *     Group:
 *       type: object
 *       properties:
 *         _id:
 *           type: string
 *           description: Group ID
 *         name:
 *           type: string
 *           description: Group name
 *         description:
 *           type: string
 *           description: Group description
 *         image:
 *           type: string
 *           description: Group image URL
 *         admin:
 *           type: string
 *           description: Main admin user ID
 *         admins:
 *           type: array
 *           items:
 *             type: string
 *           description: Array of admin user IDs
 *         members:
 *           type: array
 *           items:
 *             type: object
 *             properties:
 *               user:
 *                 type: string
 *                 description: User ID
 *               role:
 *                 type: string
 *                 enum: [admin, member]
 *               joinedAt:
 *                 type: string
 *                 format: date-time
 *         memberCount:
 *           type: number
 *           description: Total number of members
 *         settings:
 *           type: object
 *           properties:
 *             allowMembersToAddOthers:
 *               type: boolean
 *             onlyAdminsCanSend:
 *               type: boolean
 *             maxMembers:
 *               type: number
 *         isActive:
 *           type: boolean
 *         lastActivity:
 *           type: string
 *           format: date-time
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 */

/**
 * @swagger
 * /api/groups:
 *   post:
 *     summary: Create a new group
 *     tags: [Groups]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - adminId
 *             properties:
 *               name:
 *                 type: string
 *                 description: Group name
 *                 maxLength: 100
 *               description:
 *                 type: string
 *                 description: Group description
 *                 maxLength: 500
 *               image:
 *                 type: string
 *                 description: Group image URL
 *               adminId:
 *                 type: string
 *                 description: Admin user ID
 *               memberIds:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Array of initial member user IDs
 *     responses:
 *       201:
 *         description: Group created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: number
 *                   example: 201
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Group created successfully"
 *                 data:
 *                   $ref: '#/components/schemas/Group'
 *       400:
 *         description: Bad request - missing required fields
 *       404:
 *         description: Admin or member user not found
 *       500:
 *         description: Internal server error
 */
router.post('/', createGroup);

/**
 * @swagger
 * /api/groups/user/{userId}:
 *   get:
 *     summary: Get all groups for a user
 *     tags: [Groups]
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *         description: User ID
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *         description: Number of groups per page
 *     responses:
 *       200:
 *         description: User groups retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: number
 *                   example: 200
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     groups:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/Group'
 *                     pagination:
 *                       type: object
 *                       properties:
 *                         currentPage:
 *                           type: number
 *                         totalPages:
 *                           type: number
 *                         totalGroups:
 *                           type: number
 *                         hasMore:
 *                           type: boolean
 *       400:
 *         description: Bad request - User ID required
 *       500:
 *         description: Internal server error
 */
router.get('/user/:userId', getUserGroups);

/**
 * @swagger
 * /api/groups/{groupId}:
 *   get:
 *     summary: Get group details by ID
 *     tags: [Groups]
 *     parameters:
 *       - in: path
 *         name: groupId
 *         required: true
 *         schema:
 *           type: string
 *         description: Group ID
 *       - in: query
 *         name: userId
 *         schema:
 *           type: string
 *         description: User ID for access verification
 *     responses:
 *       200:
 *         description: Group details retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: number
 *                   example: 200
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/Group'
 *       400:
 *         description: Bad request - Group ID required
 *       403:
 *         description: Access denied - User not a member
 *       404:
 *         description: Group not found
 *       500:
 *         description: Internal server error
 */
router.get('/:groupId', getGroupById);

/**
 * @swagger
 * /api/groups/{groupId}/join:
 *   post:
 *     summary: Join a group
 *     tags: [Groups]
 *     parameters:
 *       - in: path
 *         name: groupId
 *         required: true
 *         schema:
 *           type: string
 *         description: Group ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - userId
 *             properties:
 *               userId:
 *                 type: string
 *                 description: User ID of the user joining the group
 *     responses:
 *       200:
 *         description: Successfully joined the group
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: number
 *                   example: 200
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Successfully joined the group"
 *                 data:
 *                   $ref: '#/components/schemas/Group'
 *       400:
 *         description: Bad request - User already member or group full
 *       404:
 *         description: Group or user not found
 *       500:
 *         description: Internal server error
 */
router.post('/:groupId/join', joinGroup);

/**
 * @swagger
 * /api/groups/{groupId}/leave:
 *   post:
 *     summary: Leave a group
 *     tags: [Groups]
 *     parameters:
 *       - in: path
 *         name: groupId
 *         required: true
 *         schema:
 *           type: string
 *         description: Group ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - userId
 *             properties:
 *               userId:
 *                 type: string
 *                 description: User ID of the user leaving the group
 *     responses:
 *       200:
 *         description: Successfully left the group
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: number
 *                   example: 200
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Successfully left the group"
 *       400:
 *         description: Bad request - User not member or admin cannot leave
 *       404:
 *         description: Group not found
 *       500:
 *         description: Internal server error
 */
router.post('/:groupId/leave', leaveGroup);

/**
 * @swagger
 * /api/groups/{groupId}/messages:
 *   get:
 *     summary: Get messages for a group
 *     tags: [Groups]
 *     parameters:
 *       - in: path
 *         name: groupId
 *         required: true
 *         schema:
 *           type: string
 *         description: Group ID
 *       - in: query
 *         name: userId
 *         schema:
 *           type: string
 *         description: User ID for access verification
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 50
 *         description: Number of messages per page
 *     responses:
 *       200:
 *         description: Group messages retrieved successfully
 *       403:
 *         description: Access denied - User not a member
 *       404:
 *         description: Group not found
 *       500:
 *         description: Internal server error
 */
router.get('/:groupId/messages', getGroupMessages);

/**
 * @swagger
 * /api/groups/{groupId}/messages:
 *   post:
 *     summary: Send a message to a group
 *     tags: [Groups]
 *     parameters:
 *       - in: path
 *         name: groupId
 *         required: true
 *         schema:
 *           type: string
 *         description: Group ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - senderId
 *               - content
 *             properties:
 *               senderId:
 *                 type: string
 *                 description: Sender user ID
 *               content:
 *                 type: string
 *                 description: Message content
 *               messageType:
 *                 type: string
 *                 enum: [text, image, audio, location, system]
 *                 default: text
 *                 description: Type of message
 *     responses:
 *       201:
 *         description: Message sent successfully
 *       400:
 *         description: Bad request - missing required fields
 *       403:
 *         description: Access denied - User not member or no send permission
 *       404:
 *         description: Group not found or inactive
 *       500:
 *         description: Internal server error
 */
router.post('/:groupId/messages', sendGroupMessage);

module.exports = router;
