const { Sequelize } = require('sequelize');

// Create Sequelize instance
const sequelize = new Sequelize(
  process.env.DATABASE_NAME || 'aldallah_db',
  process.env.DATABASE_USER || 'root',
  process.env.DATABASE_PASSWORD || '',
  {
    host: process.env.DATABASE_HOST || 'localhost',
    port: process.env.DATABASE_PORT || 3306,
    dialect: 'mysql',
    logging: process.env.NODE_ENV === 'development' ? console.log : false,
    
    // Connection pool configuration
    pool: {
      max: 10,          // Maximum number of connections
      min: 0,           // Minimum number of connections
      acquire: 30000,   // Maximum time to get connection (ms)
      idle: 10000,      // Maximum time connection can be idle (ms)
    },
    
    // Additional MySQL-specific options
    dialectOptions: {
      charset: 'utf8mb4',
      collate: 'utf8mb4_unicode_ci',
      // Enable multiple statements for migrations
      multipleStatements: true,
      // Connection timeout
      connectTimeout: 60000,
      // Socket timeout
      acquireTimeout: 60000,
      timeout: 60000,
    },
    
    // Retry configuration
    retry: {
      match: [
        /ETIMEDOUT/,
        /EHOSTUNREACH/,
        /ECONNRESET/,
        /ECONNREFUSED/,
        /ETIMEDOUT/,
        /ESOCKETTIMEDOUT/,
        /EHOSTUNREACH/,
        /EPIPE/,
        /EAI_AGAIN/,
        /ER_CON_COUNT_ERROR/,
        /ECONNREFUSED/
      ],
      max: 3
    },
    
    // Define models associations
    define: {
      timestamps: true,
      underscored: true,
      freezeTableName: true,
    }
  }
);

module.exports = sequelize;
