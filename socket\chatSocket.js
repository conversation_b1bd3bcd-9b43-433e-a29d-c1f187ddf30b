const Conversation = require('../models/Conversation');
const Message = require('../models/Message');
const Step2 = require('../models/Step2.Model');
const Group = require('../models/Group');


const activeUsers = new Map();
const userSockets = new Map();

const initializeChatSocket = (io) => {
  io.on('connection', (socket) => {
    console.log(`User connected: ${socket.id}`);


    socket.on('join', async (data) => {
      try {
        const { userId } = data;

        if (!userId) {
          socket.emit('error', { message: 'User ID is required' });
          return;
        }

        const user = await Step2.findById(userId);
        if (!user) {
          socket.emit('error', { message: 'User not found' });
          return;
        }


        activeUsers.set(userId, socket.id);
        userSockets.set(socket.id, userId);


        socket.join(`user_${userId}`);


        const conversations = await Conversation.find({
          participants: userId
        });

        conversations.forEach(conversation => {
          socket.join(`conversation_${conversation._id}`);
        });


        const groups = await Group.find({
          'members.user': userId,
          isActive: true
        });

        groups.forEach(group => {
          socket.join(`group_${group._id}`);
        });


        socket.emit('connected', {
          message: 'Successfully connected to chat',
          userId: userId
        });


        socket.broadcast.emit('user_online', { userId });

        console.log(`User ${userId} joined chat`);
      } catch (error) {
        console.error('Error in join event:', error);
        socket.emit('error', { message: 'Failed to join chat' });
      }
    });


    socket.on('send_message', async (data) => {
      try {
        const { conversationId, senderId, content, messageType = 'text', lat, lng, address } = data;


        if (!conversationId || !senderId) {
          socket.emit('error', { message: 'Conversation ID and sender ID are required' });
          return;
        }

        if (messageType === 'location') {

          if (lat === undefined || lng === undefined) {
            socket.emit('error', { message: 'Latitude and longitude are required for location messages' });
            return;
          }


          if (typeof lat !== 'number' || typeof lng !== 'number' ||
              lat < -90 || lat > 90 || lng < -180 || lng > 180) {
            socket.emit('error', { message: 'Invalid coordinates. Latitude must be between -90 and 90, longitude between -180 and 180' });
            return;
          }
        } else {

          if (!content) {
            socket.emit('error', { message: 'Content is required for text messages' });
            return;
          }
        }


        const conversation = await Conversation.findById(conversationId);
        if (!conversation) {
          socket.emit('error', { message: 'Conversation not found' });
          return;
        }

        if (!conversation.participants.includes(senderId)) {
          socket.emit('error', { message: 'Not a participant in this conversation' });
          return;
        }


        const messageData = {
          conversation: conversationId,
          sender: senderId,
          content: content || '',
          messageType,
          deliveredTo: [senderId]
        };


        if (messageType === 'location') {
          messageData.location = {
            latitude: lat,
            longitude: lng,
            address: address || ''
          };
        }

        const message = new Message(messageData);

        await message.save();
        await message.populate('sender', 'username image');


        conversation.lastMessage = message._id;


        conversation.unreadCounts.forEach(unreadCount => {
          if (unreadCount.user.toString() !== senderId) {
            unreadCount.count += 1;
          }
        });

        await conversation.save();

        io.to(`conversation_${conversationId}`).emit('new_message', {
          message,
          conversationId
        });


        const onlineParticipants = conversation.participants.filter(participantId => {
          return participantId.toString() !== senderId && activeUsers.has(participantId.toString());
        });

        if (onlineParticipants.length > 0) {
          await Message.updateOne(
            { _id: message._id },
            { $addToSet: { deliveredTo: { $each: onlineParticipants } } }
          );


          socket.emit('message_delivered', {
            messageId: message._id,
            deliveredTo: onlineParticipants
          });
        }

        console.log(`Message sent in conversation ${conversationId}`);
      } catch (error) {
        console.error('Error sending message:', error);
        socket.emit('error', { message: 'Failed to send message' });
      }
    });


    socket.on('join_conversation', async (data) => {
      try {
        const { conversationId, userId } = data;

        if (!conversationId || !userId) {
          socket.emit('error', { message: 'Conversation ID and User ID are required' });
          return;
        }


        const conversation = await Conversation.findById(conversationId);
        if (!conversation || !conversation.participants.includes(userId)) {
          socket.emit('error', { message: 'Not authorized to join this conversation' });
          return;
        }

        socket.join(`conversation_${conversationId}`);
        socket.emit('joined_conversation', { conversationId });

        console.log(`User ${userId} joined conversation ${conversationId}`);
      } catch (error) {
        console.error('Error joining conversation:', error);
        socket.emit('error', { message: 'Failed to join conversation' });
      }
    });


    socket.on('leave_conversation', (data) => {
      const { conversationId } = data;
      if (conversationId) {
        socket.leave(`conversation_${conversationId}`);
        socket.emit('left_conversation', { conversationId });
        console.log(`User left conversation ${conversationId}`);
      }
    });


    socket.on('mark_as_read', async (data) => {
      try {
        const { conversationId, userId } = data;

        if (!conversationId || !userId) {
          socket.emit('error', { message: 'Conversation ID and User ID are required' });
          return;
        }


        const result = await Message.updateMany(
          {
            conversation: conversationId,
            sender: { $ne: userId },
            readBy: { $ne: userId }
          },
          {
            $addToSet: { readBy: userId }
          }
        );


        await Conversation.updateOne(
          { _id: conversationId, 'unreadCounts.user': userId },
          { $set: { 'unreadCounts.$.count': 0 } }
        );


        socket.to(`conversation_${conversationId}`).emit('messages_read', {
          conversationId,
          readBy: userId,
          readCount: result.modifiedCount
        });

        socket.emit('marked_as_read', {
          conversationId,
          readCount: result.modifiedCount
        });

        console.log(`User ${userId} marked ${result.modifiedCount} messages as read in conversation ${conversationId}`);
      } catch (error) {
        console.error('Error marking messages as read:', error);
        socket.emit('error', { message: 'Failed to mark messages as read' });
      }
    });


    socket.on('typing_start', (data) => {
      const { conversationId, userId } = data;
      if (conversationId && userId) {
        socket.to(`conversation_${conversationId}`).emit('user_typing', {
          conversationId,
          userId,
          isTyping: true
        });
      }
    });

    socket.on('typing_stop', (data) => {
      const { conversationId, userId } = data;
      if (conversationId && userId) {
        socket.to(`conversation_${conversationId}`).emit('user_typing', {
          conversationId,
          userId,
          isTyping: false
        });
      }
    });

    socket.on('join-group', async (data) => {
      try {
        const { groupId, userId } = data;

        if (!groupId || !userId) {
          socket.emit('error', { message: 'Group ID and User ID are required' });
          return;
        }


        const group = await Group.findById(groupId);
        if (!group || !group.isActive) {
          socket.emit('error', { message: 'Group not found or inactive' });
          return;
        }

        if (!group.isUserMember(userId)) {
          socket.emit('error', { message: 'Access denied. You are not a member of this group' });
          return;
        }


        socket.join(`group_${groupId}`);


        socket.emit('group-joined', {
          groupId,
          message: `Joined group room: ${group.name}`
        });


        socket.to(`group_${groupId}`).emit('user-joined-room', {
          groupId,
          userId,
          message: `User joined the group room`
        });

        console.log(`User ${userId} joined group room ${groupId}`);
      } catch (error) {
        console.error('Error joining group room:', error);
        socket.emit('error', { message: 'Failed to join group room' });
      }
    });


    socket.on('leave-group', async (data) => {
      try {
        const { groupId, userId } = data;

        if (!groupId || !userId) {
          socket.emit('error', { message: 'Group ID and User ID are required' });
          return;
        }


        socket.leave(`group_${groupId}`);


        socket.emit('group-left', {
          groupId,
          message: 'Left group room'
        });


        socket.to(`group_${groupId}`).emit('user-left-room', {
          groupId,
          userId,
          message: 'User left the group room'
        });

        console.log(`User ${userId} left group room ${groupId}`);
      } catch (error) {
        console.error('Error leaving group room:', error);
        socket.emit('error', { message: 'Failed to leave group room' });
      }
    });


    socket.on('send-group-message', async (data) => {
      try {
        const { groupId, senderId, content, messageType = 'text', lat, lng, address } = data;


        if (!groupId || !senderId) {
          socket.emit('error', { message: 'Group ID and sender ID are required' });
          return;
        }


        if (messageType === 'location') {

          if (lat === undefined || lng === undefined) {
            socket.emit('error', { message: 'Latitude and longitude are required for location messages' });
            return;
          }

          if (typeof lat !== 'number' || typeof lng !== 'number' ||
              lat < -90 || lat > 90 || lng < -180 || lng > 180) {
            socket.emit('error', { message: 'Invalid coordinates. Latitude must be between -90 and 90, longitude between -180 and 180' });
            return;
          }
        } else {

          if (!content) {
            socket.emit('error', { message: 'Content is required for text messages' });
            return;
          }
        }


        const group = await Group.findById(groupId).populate('conversation');
        if (!group || !group.isActive) {
          socket.emit('error', { message: 'Group not found or inactive' });
          return;
        }

        if (!group.isUserMember(senderId)) {
          socket.emit('error', { message: 'Access denied. You are not a member of this group' });
          return;
        }


        if (group.settings.onlyAdminsCanSend && !group.isUserAdmin(senderId)) {
          socket.emit('error', { message: 'Only group admins can send messages in this group' });
          return;
        }


        const messageData = {
          conversation: group.conversation._id,
          sender: senderId,
          content: content || '',
          messageType,
          deliveredTo: [senderId]
        };


        if (messageType === 'location') {
          messageData.location = {
            latitude: lat,
            longitude: lng,
            address: address || ''
          };
        }


        const message = new Message(messageData);

        await message.save();
        await message.populate('sender', 'username image');


        const conversation = group.conversation;
        conversation.lastMessage = message._id;


        conversation.unreadCounts.forEach(unreadCount => {
          if (unreadCount.user.toString() !== senderId) {
            unreadCount.count += 1;
          }
        });

        await conversation.save();


        group.lastActivity = new Date();
        await group.save();


        io.to(`group_${groupId}`).emit('receive-message', {
          message,
          groupId,
          groupName: group.name
        });


        const onlineMembers = group.members.filter(member => {
          return member.user.toString() !== senderId && activeUsers.has(member.user.toString());
        }).map(member => member.user);

        if (onlineMembers.length > 0) {
          await Message.updateOne(
            { _id: message._id },
            { $addToSet: { deliveredTo: { $each: onlineMembers } } }
          );


          socket.emit('message-delivered', {
            messageId: message._id,
            groupId,
            deliveredTo: onlineMembers
          });
        }

        console.log(`Group message sent in group ${groupId} by user ${senderId}`);
      } catch (error) {
        console.error('Error sending group message:', error);
        socket.emit('error', { message: 'Failed to send group message' });
      }
    });


    socket.on('group-typing', (data) => {
      try {
        const { groupId, userId, isTyping } = data;

        if (!groupId || !userId) {
          socket.emit('error', { message: 'Group ID and User ID are required' });
          return;
        }


        socket.to(`group_${groupId}`).emit('user-typing', {
          groupId,
          userId,
          isTyping
        });
      } catch (error) {
        console.error('Error handling group typing:', error);
      }
    });


    socket.on('disconnect', () => {
      const userId = userSockets.get(socket.id);

      if (userId) {

        activeUsers.delete(userId);
        userSockets.delete(socket.id);


        socket.broadcast.emit('user_offline', { userId });

        console.log(`User ${userId} disconnected`);
      } else {
        console.log(`Unknown user disconnected: ${socket.id}`);
      }
    });


    socket.on('error', (error) => {
      console.error('Socket error:', error);
    });
  });


  const getOnlineUsers = () => {
    return Array.from(activeUsers.keys());
  };


  const isUserOnline = (userId) => {
    return activeUsers.has(userId);
  };

  return {
    getOnlineUsers,
    isUserOnline,
    activeUsers,
    userSockets
  };
};

module.exports = initializeChatSocket;
