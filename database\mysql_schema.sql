-- MySQL Schema for Aldallah Backend Application
-- Migration from MongoDB to MySQL

-- Create database (run this separately if needed)
-- CREATE DATABASE aldallah_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
-- USE aldallah_db;

-- Drop tables in reverse order of dependencies to avoid foreign key constraints
DROP TABLE IF EXISTS group_members;
DROP TABLE IF EXISTS message_read_status;
DROP TABLE IF EXISTS message_delivery_status;
DROP TABLE IF EXISTS messages;
DROP TABLE IF EXISTS conversation_unread_counts;
DROP TABLE IF EXISTS conversation_participants;
DROP TABLE IF EXISTS conversations;
DROP TABLE IF EXISTS groups;
DROP TABLE IF EXISTS step2_users;
DROP TABLE IF EXISTS step1_users;

-- Step1 Users Table (Initial registration data)
CREATE TABLE step1_users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    language VARCHAR(50) NOT NULL,
    country VARCHAR(100) NOT NULL,
    phone_number VARCHAR(20) NOT NULL,
    email VARCHAR(255) NOT NULL UNIQUE,
    otp VARCHAR(10),
    otp_created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_email (email),
    INDEX idx_phone (phone_number),
    INDEX idx_otp_created (otp_created_at)
);

-- Step2 Users Table (Complete user profiles with chat features)
CREATE TABLE step2_users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    step1_id INT NOT NULL,
    language VARCHAR(50) NOT NULL,
    country VARCHAR(100) NOT NULL,
    phone_number VARCHAR(20) NOT NULL,
    temp_phone_number VARCHAR(20),
    email VARCHAR(255) NOT NULL UNIQUE,
    username VARCHAR(100) NOT NULL,
    image TEXT NOT NULL,
    otp VARCHAR(10),
    otp_created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    -- Chat-related fields
    is_online BOOLEAN DEFAULT FALSE,
    last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    socket_id VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (step1_id) REFERENCES step1_users(id) ON DELETE CASCADE,
    INDEX idx_step1_id (step1_id),
    INDEX idx_email (email),
    INDEX idx_username (username),
    INDEX idx_phone (phone_number),
    INDEX idx_online_status (is_online),
    INDEX idx_socket_id (socket_id)
);

-- Conversations Table
CREATE TABLE conversations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    is_group BOOLEAN DEFAULT FALSE,
    last_message_id INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_is_group (is_group),
    INDEX idx_updated_at (updated_at)
);

-- Conversation Participants (Many-to-Many relationship)
CREATE TABLE conversation_participants (
    id INT PRIMARY KEY AUTO_INCREMENT,
    conversation_id INT NOT NULL,
    user_id INT NOT NULL,
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (conversation_id) REFERENCES conversations(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES step2_users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_conversation_user (conversation_id, user_id),
    INDEX idx_conversation_id (conversation_id),
    INDEX idx_user_id (user_id)
);

-- Conversation Unread Counts
CREATE TABLE conversation_unread_counts (
    id INT PRIMARY KEY AUTO_INCREMENT,
    conversation_id INT NOT NULL,
    user_id INT NOT NULL,
    count INT DEFAULT 0,
    
    FOREIGN KEY (conversation_id) REFERENCES conversations(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES step2_users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_conversation_user_count (conversation_id, user_id),
    INDEX idx_conversation_id (conversation_id),
    INDEX idx_user_id (user_id)
);

-- Messages Table
CREATE TABLE messages (
    id INT PRIMARY KEY AUTO_INCREMENT,
    conversation_id INT NOT NULL,
    sender_id INT NOT NULL,
    content TEXT,
    message_type ENUM('text', 'image', 'audio', 'location', 'system') DEFAULT 'text',
    media_url TEXT,
    -- Location data (JSON-like structure flattened)
    location_latitude DECIMAL(10, 8),
    location_longitude DECIMAL(11, 8),
    location_address TEXT,
    is_deleted BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (conversation_id) REFERENCES conversations(id) ON DELETE CASCADE,
    FOREIGN KEY (sender_id) REFERENCES step2_users(id) ON DELETE CASCADE,
    INDEX idx_conversation_id (conversation_id),
    INDEX idx_sender_id (sender_id),
    INDEX idx_message_type (message_type),
    INDEX idx_created_at (created_at),
    INDEX idx_is_deleted (is_deleted)
);

-- Message Read Status (Many-to-Many relationship)
CREATE TABLE message_read_status (
    id INT PRIMARY KEY AUTO_INCREMENT,
    message_id INT NOT NULL,
    user_id INT NOT NULL,
    read_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (message_id) REFERENCES messages(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES step2_users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_message_user_read (message_id, user_id),
    INDEX idx_message_id (message_id),
    INDEX idx_user_id (user_id)
);

-- Message Delivery Status (Many-to-Many relationship)
CREATE TABLE message_delivery_status (
    id INT PRIMARY KEY AUTO_INCREMENT,
    message_id INT NOT NULL,
    user_id INT NOT NULL,
    delivered_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (message_id) REFERENCES messages(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES step2_users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_message_user_delivery (message_id, user_id),
    INDEX idx_message_id (message_id),
    INDEX idx_user_id (user_id)
);

-- Groups Table
CREATE TABLE groups (
    id INT PRIMARY KEY AUTO_INCREMENT,
    conversation_id INT NOT NULL UNIQUE,
    name VARCHAR(100) NOT NULL,
    description VARCHAR(500),
    image TEXT,
    admin_id INT NOT NULL,
    -- Settings
    allow_members_to_add_others BOOLEAN DEFAULT FALSE,
    only_admins_can_send BOOLEAN DEFAULT FALSE,
    max_members INT DEFAULT 256 CHECK (max_members >= 2 AND max_members <= 1000),
    is_active BOOLEAN DEFAULT TRUE,
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (conversation_id) REFERENCES conversations(id) ON DELETE CASCADE,
    FOREIGN KEY (admin_id) REFERENCES step2_users(id) ON DELETE CASCADE,
    INDEX idx_conversation_id (conversation_id),
    INDEX idx_admin_id (admin_id),
    INDEX idx_is_active (is_active),
    INDEX idx_last_activity (last_activity)
);

-- Group Members Table (handles both regular members and admins)
CREATE TABLE group_members (
    id INT PRIMARY KEY AUTO_INCREMENT,
    group_id INT NOT NULL,
    user_id INT NOT NULL,
    role ENUM('admin', 'member') DEFAULT 'member',
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (group_id) REFERENCES groups(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES step2_users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_group_user (group_id, user_id),
    INDEX idx_group_id (group_id),
    INDEX idx_user_id (user_id),
    INDEX idx_role (role)
);

-- Add foreign key constraint for last_message_id after messages table is created
ALTER TABLE conversations 
ADD CONSTRAINT fk_conversations_last_message 
FOREIGN KEY (last_message_id) REFERENCES messages(id) ON DELETE SET NULL;

-- Create additional indexes for performance optimization
CREATE INDEX idx_step2_users_online_last_seen ON step2_users(is_online, last_seen);
CREATE INDEX idx_messages_conversation_created ON messages(conversation_id, created_at);
CREATE INDEX idx_groups_active_last_activity ON groups(is_active, last_activity);
CREATE INDEX idx_conversation_participants_user_conversation ON conversation_participants(user_id, conversation_id);

-- Insert sample data (optional - remove in production)
-- This is just for testing purposes
/*
INSERT INTO step1_users (language, country, phone_number, email) VALUES
('English', 'USA', '+1234567890', '<EMAIL>');

INSERT INTO step2_users (step1_id, language, country, phone_number, email, username, image) VALUES
(1, 'English', 'USA', '+1234567890', '<EMAIL>', 'testuser', 'https://example.com/image.jpg');
*/
