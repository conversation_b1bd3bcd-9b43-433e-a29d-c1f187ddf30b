const Step2 = require('../models/Step2.Model');
const Step1 = require('../models/Step1.Model');
const cloudinary = require('../services/cloudinary');
const jwt = require('jsonwebtoken');
const sendOtpEmail = require('../utils/sendOtpEmail.js');

const generateOTP = () => {
  return Math.floor(1000 + Math.random() * 9000).toString();
};

const uploadUser = async (req, res) => {
    try {
        const { username, email , User_id } = req.body;
        const image = req.file;

        if (!username || !email || !email.includes('@')||!User_id) {

            return res.status(400).json({
                status: 400,
                success: false,
                message: 'Username and valid email are required and User_id is required'
            });

        }

        const fineUser = await Step1.findOne({ email: email ,_id:User_id});

        if (!fineUser) {
            return res.status(404).json({
                status: 404,
                success: false,
                message: 'User not found'
            });
        }


        const imageBase64 = image.buffer.toString('base64');
        const mimeType = image.mimetype;
        const imageData = `data:${mimeType};base64,${imageBase64}`;


        const cloudinaryResponse = await cloudinary.uploader.upload(imageData);
        const cloudinaryImageUrl = cloudinaryResponse.secure_url;



        const newUser = new Step2({
            step1_id:fineUser._id,
            language: fineUser.language,
            country: fineUser.country,
            phoneNumber: fineUser.phoneNumber,
            email: fineUser.email,
            otp: fineUser.otp,
            otpCreatedAt: fineUser.otpCreatedAt,
            username,
            image: cloudinaryImageUrl,
        });

        await newUser.save();

          const token = jwt.sign({ userId: newUser._id,email: newUser.email,username:newUser.username ,image:newUser.image }, process.env.JWT_SECRET, {
            expiresIn: '1d',
        });


        return res.status(201).json({
            status: 201,
            success: true,
            message: 'User uploaded successfully',
            data: newUser,
            token,
        });



    } catch (error) {
        return res.status(500).json({
            status: 500,
            success: false,
            message: 'Internal Server Error',
            error: error.message,
        });
    }
};


const updateUser = async (req, res) => {
    try {
        const { id } = req.params;
        const { username, email } = req.body;
        const image = req.file;

        if (!username || !email || !email.includes('@')) {
            return res.status(400).json({
                status: 400,
                success: false,
                message: 'Username and a valid email are required'
            });
        }


        const user = await Step2.findById(id);
         const Step1User = await Step1.findById(user.step1_id);

        if (!user) {
            return res.status(404).json({
                status: 404,
                success: false,
                message: 'User not found'
            });
        }

        user.username = username;
        user.email = email;

        if (image) {
            const imageBase64 = image.buffer.toString('base64');
            const mimeType = image.mimetype;
            const imageData = `data:${mimeType};base64,${imageBase64}`;

            
            const cloudinaryResponse = await cloudinary.uploader.upload(imageData);
            const cloudinaryImageUrl = cloudinaryResponse.secure_url;
            user.image = cloudinaryImageUrl;


        }

        await user.save();
        await Step1.findByIdAndUpdate(Step1User._id,{
            email:email,
        },{new:true})


        return res.status(200).json({
            status: 200,
            success: true,
            message: 'User updated successfully',
            data: user,
        });

    } catch (error) {
        return res.status(500).json({
            status: 500,
            success: false,
            message: 'Internal Server Error',
            error: error.message,
        });
    }
};

const getAlluser=async(req, res) => {
    try {
        const users = await Step2.find();
        res.status(200).json({
            status: 200,
            success: true, data: users });
    }catch (error) {
        return res.status(500).json({
            status: 500,
            success: false,
            message: 'Internal Server Error',
            error: error.message,
        });
    }
}

const getSpecific_user=async(req, res) => {
    try {
        const { id } = req.params;
        const user = await Step2.findById(id);
        if (!user) {
            return res.status(404).json({
                status: 404,
                success: false,
                message: 'User not found'
            });
        }
        res.status(200).json({
            status: 200,
            success: true,
            message: 'User found',
            data: user });

    }catch (error) {
        return res.status(500).json({
            status: 500,
            success: false,
            message: 'Internal Server Error',
            error: error.message,
        });
    }
}

const deleteUser=async(req, res) => {
    try {
        const id = req.user._id;

        const user = await Step2.findById(id);
        if (!user) {
            return res.status(404).json({
                status: 404,
                success: false,
                message: 'User not found'
            });
        }
        await Step2.findByIdAndDelete(id);
        await Step1.findByIdAndDelete(user.step1_id);
        res.status(200).json({
            status: 200,
            success: true,
            message: 'User deleted successfully',
            data: user });
    }catch (error) {
        return res.status(500).json({
            status: 500,
            success: false,
            message: 'Internal Server Error',
            error: error.message,
        });
    }
}

const changePhoneNumber = async (req, res) => {
  try {
    const { phoneNumber } = req.body;
    const userId = req.user._id;


    const user = await Step2.findById(userId);
    if (!user) {
      return res.status(404).json({
        status: 404,
        success: false,
        message: 'User not found'
      });
    }


    if (user.phoneNumber === phoneNumber) {
      return res.status(400).json({
        status: 400,
        success: false,
        message: 'New phone number cannot be the same as the old one'
      });
    }

    const sendOTP = generateOTP();
    await sendOtpEmail(user.email, sendOTP);

    user.temp_phoneNumber = phoneNumber;
    user.otp = sendOTP;
    user.otpCreatedAt = Date.now();
    await user.save();

    const step1User = await Step1.findById(user.step1_id);
    step1User.otp = sendOTP;
    step1User.otpCreatedAt = Date.now();
    await step1User.save();

    return res.status(200).json({
      status: 200,
      success: true,
      message: 'OTP sent successfully',
      data: user,
    });
  } catch (error) {
    return res.status(500).json({
      status: 500,
      success: false,
      message: 'Internal Server Error',
      error: error.message,
    });
  }
};

const verifyOTP = async (req, res) => {
  try {
    const { otp } = req.body;
    const userId = req.user._id;

    const user = await Step2.findById(userId);
    if (!user) {
      return res.status(404).json({
        status: 404,
        success: false,
        message: 'User not found'
      });
    }

    if (user.otp !== otp) {
      return res.status(401).json({
        status: 401,
        success: false,
        message: 'Invalid OTP'
      });
    }

    const otpAge = (Date.now() - user.otpCreatedAt) / 1000; // in seconds
    if (otpAge > 60) {
      return res.status(401).json({
        status: 401,
        success: false,
        message: 'OTP expired'
      });
    }

    const step1User = await Step1.findById(user.step1_id);
    if (step1User) {
      step1User.phoneNumber = user.temp_phoneNumber;
      await step1User.save();
    }

    user.phoneNumber = user.temp_phoneNumber;
    user.temp_phoneNumber = undefined;
    user.otp = undefined;
    user.otpCreatedAt = undefined;
    await user.save();

    return res.status(200).json({
      status: 200,
      success: true,
      message: 'Phone number changed successfully',
      data: user,
    });

  } catch (error) {
    return res.status(500).json({
      status: 500,
      success: false,
      message: 'Internal Server Error',
      error: error.message,
    });
  }
};
module.exports = {
    uploadUser,
    updateUser,
    getAlluser,
    getSpecific_user,
    deleteUser,
    changePhoneNumber,
    verifyOTP,
};
