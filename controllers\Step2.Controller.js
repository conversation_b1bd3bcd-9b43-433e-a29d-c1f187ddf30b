const { Step1, Step2 } = require('../models');
const cloudinary = require('../services/cloudinary');
const jwt = require('jsonwebtoken');
const sendOtpEmail = require('../utils/sendOtpEmail.js');

const generateOTP = () => {
  return Math.floor(1000 + Math.random() * 9000).toString();
};

const uploadUser = async (req, res) => {
    try {
        const { username, email, User_id } = req.body;
        const image = req.file;

        if (!username || !email || !email.includes('@') || !User_id) {
            return res.status(400).json({
                status: 400,
                success: false,
                message: 'Username and valid email are required and User_id is required'
            });
        }

        const fineUser = await Step1.findOne({
            where: {
                email: email,
                id: User_id
            }
        });

        if (!fineUser) {
            return res.status(404).json({
                status: 404,
                success: false,
                message: 'User not found'
            });
        }

        // Check if user already exists
        const existingUser = await Step2.findOne({
            where: { step1Id: User_id }
        });
        if (existingUser) {
            return res.status(409).json({
                status: 409,
                success: false,
                message: 'User already exists for this step1_id',
            });
        }

        // Upload image to Cloudinary
        const imageBase64 = image.buffer.toString('base64');
        const mimeType = image.mimetype;
        const imageData = `data:${mimeType};base64,${imageBase64}`;

        const cloudinaryResponse = await cloudinary.uploader.upload(imageData);
        const cloudinaryImageUrl = cloudinaryResponse.secure_url;

        // Create new Step2 user
        const newUser = await Step2.create({
            step1Id: fineUser.id,
            language: fineUser.language,
            country: fineUser.country,
            phoneNumber: fineUser.phoneNumber,
            email: fineUser.email,
            otp: fineUser.otp,
            otpCreatedAt: fineUser.otpCreatedAt,
            username,
            image: cloudinaryImageUrl,
        });

        const token = jwt.sign({
            userId: newUser.id,
            email: newUser.email,
            username: newUser.username,
            image: newUser.image
        }, process.env.JWT_SECRET, {
            expiresIn: '1d',
        });

        return res.status(201).json({
            status: 201,
            success: true,
            message: 'User uploaded successfully',
            data: newUser,
            token,
        });

    } catch (error) {
        // Handle unique constraint errors
        if (error.name === 'SequelizeUniqueConstraintError') {
            return res.status(400).json({
                status: 400,
                success: false,
                message: 'Email already exists',
            });
        }
        return res.status(500).json({
            status: 500,
            success: false,
            message: 'Internal Server Error',
            error: error.message,
        });
    }
};


const updateUser = async (req, res) => {
    try {
        const { id } = req.params;
        const { username, email } = req.body;
        const image = req.file;

        if (!username || !email || !email.includes('@')) {
            return res.status(400).json({
                status: 400,
                success: false,
                message: 'Username and a valid email are required'
            });
        }


        const user = await Step2.findById(id);
         const Step1User = await Step1.findById(user.step1_id);

        if (!user) {
            return res.status(404).json({
                status: 404,
                success: false,
                message: 'User not found'
            });
        }

        user.username = username;
        user.email = email;

        if (image) {
            const imageBase64 = image.buffer.toString('base64');
            const mimeType = image.mimetype;
            const imageData = `data:${mimeType};base64,${imageBase64}`;


            const cloudinaryResponse = await cloudinary.uploader.upload(imageData);
            const cloudinaryImageUrl = cloudinaryResponse.secure_url;
            user.image = cloudinaryImageUrl;


        }

        await user.save();
        await Step1.findByIdAndUpdate(Step1User._id,{
            email:email,
        },{new:true})


        return res.status(200).json({
            status: 200,
            success: true,
            message: 'User updated successfully',
            data: user,
        });

    } catch (error) {
        return res.status(500).json({
            status: 500,
            success: false,
            message: 'Internal Server Error',
            error: error.message,
        });
    }
};

const getAlluser = async (req, res) => {
    try {
        const users = await Step2.findAll({
            include: [{
                model: Step1,
                as: 'step1User',
                attributes: ['language', 'country']
            }],
            order: [['createdAt', 'DESC']]
        });
        res.status(200).json({
            status: 200,
            success: true,
            data: users
        });
    } catch (error) {
        return res.status(500).json({
            status: 500,
            success: false,
            message: 'Internal Server Error',
            error: error.message,
        });
    }
}

const getSpecific_user = async (req, res) => {
    try {
        const { id } = req.params;
        const user = await Step2.findByPk(id, {
            include: [{
                model: Step1,
                as: 'step1User',
                attributes: ['language', 'country']
            }]
        });
        if (!user) {
            return res.status(404).json({
                status: 404,
                success: false,
                message: 'User not found'
            });
        }
        res.status(200).json({
            status: 200,
            success: true,
            message: 'User found',
            data: user
        });

    } catch (error) {
        return res.status(500).json({
            status: 500,
            success: false,
            message: 'Internal Server Error',
            error: error.message,
        });
    }
}

const deleteUser = async (req, res) => {
    try {
        const id = req.user.userId; // Updated to match JWT payload

        const user = await Step2.findByPk(id);
        if (!user) {
            return res.status(404).json({
                status: 404,
                success: false,
                message: 'User not found'
            });
        }

        // Delete Step2 user (Step1 will be deleted due to CASCADE)
        await Step2.destroy({
            where: { id: id }
        });

        res.status(200).json({
            status: 200,
            success: true,
            message: 'User deleted successfully',
            data: user
        });
    } catch (error) {
        return res.status(500).json({
            status: 500,
            success: false,
            message: 'Internal Server Error',
            error: error.message,
        });
    }
}

const changePhoneNumber = async (req, res) => {
  try {
    const { phoneNumber } = req.body;
    const userId = req.user.userId; // Updated to match JWT payload

    const user = await Step2.findByPk(userId);
    if (!user) {
      return res.status(404).json({
        status: 404,
        success: false,
        message: 'User not found'
      });
    }

    if (user.phoneNumber === phoneNumber) {
      return res.status(400).json({
        status: 400,
        success: false,
        message: 'New phone number cannot be the same as the old one'
      });
    }

    const sendOTP = generateOTP();
    await sendOtpEmail(user.email, sendOTP);

    // Update user with temporary phone number and OTP
    await user.update({
      tempPhoneNumber: phoneNumber,
      otp: sendOTP,
      otpCreatedAt: new Date(),
    });

    // Update Step1 user as well
    const step1User = await Step1.findByPk(user.step1Id);
    if (step1User) {
      await step1User.update({
        otp: sendOTP,
        otpCreatedAt: new Date(),
      });
    }

    return res.status(200).json({
      status: 200,
      success: true,
      message: 'OTP sent successfully',
      data: user,
    });
  } catch (error) {
    return res.status(500).json({
      status: 500,
      success: false,
      message: 'Internal Server Error',
      error: error.message,
    });
  }
};

const verifyOTP = async (req, res) => {
  try {
    const { otp } = req.body;
    const userId = req.user.userId; // Updated to match JWT payload

    const user = await Step2.findByPk(userId);
    if (!user) {
      return res.status(404).json({
        status: 404,
        success: false,
        message: 'User not found'
      });
    }

    if (user.otp !== otp) {
      return res.status(401).json({
        status: 401,
        success: false,
        message: 'Invalid OTP'
      });
    }

    const otpAge = (Date.now() - new Date(user.otpCreatedAt).getTime()) / 1000; // in seconds
    if (otpAge > 60) {
      return res.status(401).json({
        status: 401,
        success: false,
        message: 'OTP expired'
      });
    }

    // Update Step1 user phone number
    const step1User = await Step1.findByPk(user.step1Id);
    if (step1User) {
      await step1User.update({
        phoneNumber: user.tempPhoneNumber,
      });
    }

    // Update Step2 user and clear temporary data
    await user.update({
      phoneNumber: user.tempPhoneNumber,
      tempPhoneNumber: null,
      otp: null,
      otpCreatedAt: null,
    });

    return res.status(200).json({
      status: 200,
      success: true,
      message: 'Phone number changed successfully',
      data: user,
    });

  } catch (error) {
    return res.status(500).json({
      status: 500,
      success: false,
      message: 'Internal Server Error',
      error: error.message,
    });
  }
};
module.exports = {
    uploadUser,
    updateUser,
    getAlluser,
    getSpecific_user,
    deleteUser,
    changePhoneNumber,
    verifyOTP,
};
