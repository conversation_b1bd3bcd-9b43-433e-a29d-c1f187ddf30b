const Step2 = require('../models/Step2.Model');
const jwt = require('jsonwebtoken');

const loginUser = async (req, res) => {
    try {
        const {phoneNumber} = req.body;
        if(!phoneNumber){
            return res.status(400).json({
                status: 400,
                success: false,
                message: 'Phone number is required'
            });
        }
        const user = await Step2.findOne({phoneNumber});
        if(!user){
            return res.status(404).json({
                status: 404,
                success: false,
                message: 'Phone number not found'

            });
        }
        const token = jwt.sign({ userId: user._id,email: user.email,username:user.username ,image:user.image }, process.env.JWT_SECRET, {
            expiresIn: '1d',
        });
        return res.status(200).json({
            status: 200,
            success: true,
            message: 'User logged in successfully',
            data: user,
            token,
        });
    }catch (error) {
        return res.status(500).json({
            status: 500,
            success: false,
            message: 'Internal Server Error',
            error: error.message,
        });
    }
}
module.exports = {loginUser};
